# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake STAC Ingester with optimized Parquet settings and real COG header parsing.

This module implements a production-ready STAC ingestion pipeline that:
- Uses delta-rs for ACID transactions and schema evolution
- Integrates existing aiohttp COG header parsing (no major changes)
- Applies advanced Parquet optimization for repeated data compression
- Implements year/month temporal partitioning
- Supports spatial indexing with S2 cells and GeoArrow encoding
"""

import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

import pyarrow as pa
from deltalake import (
    DeltaTable,
    WriterProperties,
    ColumnProperties,
    BloomFilterProperties,
)
import pystac_client

# Import our optimized schemas and settings
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema, StacSchemaConfig
from data_marketplace.ingestion.restart_manager import RestartManager
from data_marketplace.ingestion.simple_date_checker import SimpleDateChecker
from data_marketplace.ingestion.streaming_processor import StreamingStacProcessor
from data_marketplace.cog.async_tiff_parser import AsyncTiffStacCogProcessor

# Removed performance_config - using hardcoded optimal values
from data_marketplace.storage.delta_manager import DeltaManager
from data_marketplace.config.parquet_optimization import (
    get_optimized_stac_settings,
    get_delta_lake_properties,
)


logger = logging.getLogger(__name__)


class DeltaStacIngester:
    """
    Production-ready unified STAC + COG ingester using Delta Lake.

    Features:
    - Single unified table (one row per COG asset with repeated scene metadata)
    - Real STAC data ingestion with existing aiohttp COG parsing
    - Delta Lake ACID transactions with delta-rs
    - Advanced Parquet optimization for repeated data compression
    - Year/month temporal partitioning
    - S2 spatial indexing with native GeoArrow encoding
    - Optimized for cross-collection spatial + technical queries
    - Scalable from 10k to 100M+ records
    """

    # Lightweight memory diagnostics without external deps
    @staticmethod
    def _log_memory(context: str = "") -> None:
        try:
            import os
            import gc

            # Try resource on Unix
            try:
                import resource

                usage = resource.getrusage(resource.RUSAGE_SELF)
                # ru_maxrss is kilobytes on Linux, bytes on macOS; normalize to MB
                ru = usage.ru_maxrss
                mb = ru / 1024.0 if ru > 1024 * 1024 else ru / 1024.0
                logger.debug(
                    f"[mem] RSS max ~{mb:.1f} MB (ru_maxrss), gc objects: gen0={len(gc.get_objects())}"
                )
            except Exception:
                pass
            # /proc/self/statm (Linux)
            if os.path.exists("/proc/self/statm"):
                with open("/proc/self/statm") as f:
                    parts = f.read().split()
                    if len(parts) >= 2:
                        pages = int(parts[1])
                        page_size = os.sysconf("SC_PAGE_SIZE")
                        rss_mb = pages * page_size / (1024 * 1024)
                        logger.info(f"[mem] {context} RSS ~{rss_mb:.1f} MB")
        except Exception:
            pass

    def __init__(
        self,
        unified_table_path: str,
        config: Optional[StacSchemaConfig] = None,
        storage_options: Optional[Dict[str, str]] = None,
        stac_api_url: Optional[str] = None,
    ):
        """
        Initialize the unified Delta STAC ingester.

        Args:
            unified_table_path: Path to Delta Lake unified STAC + COG table
            config: Schema configuration
            storage_options: S3/cloud storage options for Delta Lake
            stac_api_url: STAC API URL for federation metadata

        """
        self.unified_table_path = unified_table_path
        self.config = config or StacSchemaConfig()
        self.storage_options = storage_options or {}
        self.stac_api_url = stac_api_url

        # Initialize unified schema
        self.unified_schema = UnifiedStacSchema(self.config)

        # Initialize modular components
        self.restart_manager = RestartManager(unified_table_path, storage_options)
        self.cog_processor = AsyncTiffStacCogProcessor()

        # Initialize streaming processor with auto-tuned settings based on CPU cores
        logger.info("Using streaming processor with auto-tuned producer-consumer architecture")
        self.stac_processor = StreamingStacProcessor(
            self.unified_schema,
            self.cog_processor,
            existing_key_checker=None,
            # Auto-tuned parameters based on first principles (CPU cores, batch size, etc.)
            # max_buffer_size=None,  # Auto-tuned based on CPU cores
            # n_consumers=None,      # Auto-tuned based on CPU cores
        )

        self.delta_manager = DeltaManager()

        # Get optimization settings for unified table
        self.parquet_settings = (
            get_optimized_stac_settings()
        )  # Use STAC settings as base
        self.delta_properties = get_delta_lake_properties()

        # Update dictionary encoding for unified schema
        self.parquet_settings["use_dictionary"] = (
            self.unified_schema.get_dictionary_columns()
        )

        logger.info("Initialized Unified Delta STAC Ingester")
        logger.info(f"Unified table: {unified_table_path}")
        logger.info(
            f"Dictionary encoding enabled for {len(self.parquet_settings['use_dictionary'])} columns"
        )
        logger.info(
            f"Bloom filters enabled for {len(self.unified_schema.get_bloom_filter_columns())} columns"
        )

        # Log hardcoded optimal configuration
        logger.info(
            "Performance config: streaming processor with hardcoded optimal settings, "
            "200 COG concurrency, 5000 batch size"
        )

    def clear_caches(self):
        """Clear all processor caches for fair performance comparison."""
        if hasattr(self.stac_processor, "_existing_keys_cache"):
            self.stac_processor._existing_keys_cache.clear()
            logger.debug("Cleared processor existing_keys_cache")

        if hasattr(self.stac_processor, "clear_existing_keys_cache"):
            self.stac_processor.clear_existing_keys_cache()
            logger.debug("Cleared processor cache via clear_existing_keys_cache method")

    async def ingest_stac_collection(
        self,
        stac_api_url: str,
        collection_id: str,
        max_items: Optional[int] = None,
        datetime_range: Optional[str] = None,
        bbox: Optional[List[float]] = None,
        batch_size: int = 2000,
        max_concurrent_cog_requests: Optional[int] = None,  # Auto-tuned if None
        max_concurrent_stac_items: Optional[int] = None,    # Auto-tuned if None
        optimize_every_n_batches: int = 100,
        force_reingest: bool = False,  # Force MERGE mode instead of APPEND
        vacuum_retention_hours: int = 168,  # 7 days default
        skip_vacuum: bool = False,
        skip_optimize: bool = False,
        io_optimized: bool = False,  # Use aggressive I/O-optimized concurrency
        _skip_fast_check: bool = False,  # Internal parameter to avoid recursion
    ) -> Dict[str, Any]:
        """
        Ingest STAC collection into unified table with real data and COG header parsing.

        Args:
            stac_api_url: STAC API endpoint URL
            collection_id: Collection to ingest (e.g., 'sentinel-2-l2a')
            max_items: Maximum items to ingest (for testing)
            datetime_range: Date range filter (e.g., '2023-01-01/2023-12-31')
            bbox: Bounding box filter [west, south, east, north]
            batch_size: Items per batch for processing (default: 2000)
            max_concurrent_cog_requests: Max concurrent COG header requests (auto-tuned if None)
            max_concurrent_stac_items: Max concurrent STAC items processed in parallel (auto-tuned if None)
            optimize_every_n_batches: Run optimization every N batches (default: 100)
            force_reingest: Force MERGE mode instead of APPEND (allows overwriting existing data)
            vacuum_retention_hours: Hours to retain old files before vacuum cleanup (default: 168 = 7 days)
            skip_vacuum: Skip vacuum cleanup of old/orphan files
            skip_optimize: Skip table optimization/compaction
            io_optimized: Use aggressive I/O-optimized concurrency (inspired by tiff-dumper)

        Returns:
            Ingestion statistics and performance metrics
        """
        start_time = datetime.now()
        stats = {
            "collection_id": collection_id,
            "start_time": start_time.isoformat(),
            "stac_items_processed": 0,
            "cog_assets_processed": 0,
            "unified_records_written": 0,  # One record per COG asset
            "errors": [],
            "performance": {},
        }

        # Auto-tune concurrency parameters based on CPU cores and I/O optimization mode
        import os
        cpu_count = os.cpu_count() or 4

        if max_concurrent_cog_requests is None:
            if io_optimized:
                # Aggressive I/O-optimized mode (respecting file descriptor limits)
                max_concurrent_cog_requests = min(1000, cpu_count * 250)  # Aggressive but safe for file descriptors
                logger.info(f"I/O-optimized max_concurrent_cog_requests: {max_concurrent_cog_requests} (aggressive, file-descriptor safe)")
            else:
                # Conservative auto-tuning
                max_concurrent_cog_requests = cpu_count * 20  # 20 requests per core (I/O bound)
                logger.info(f"Auto-tuned max_concurrent_cog_requests to {max_concurrent_cog_requests} (based on {cpu_count} CPU cores)")

        if max_concurrent_stac_items is None:
            if io_optimized:
                # More aggressive STAC processing for I/O-optimized mode
                max_concurrent_stac_items = max(50, cpu_count * 10)  # 10x more aggressive
                logger.info(f"I/O-optimized max_concurrent_stac_items: {max_concurrent_stac_items}")
            else:
                # Conservative auto-tuning
                max_concurrent_stac_items = max(5, cpu_count // 2)  # Conservative for STAC processing
                logger.info(f"Auto-tuned max_concurrent_stac_items to {max_concurrent_stac_items} (based on {cpu_count} CPU cores)")

        # Reinitialize streaming processor if I/O-optimized mode is requested
        if io_optimized:
            logger.info("Reinitializing streaming processor for I/O-optimized mode...")
            # Create new COG processor for I/O-optimized mode to avoid resource conflicts
            from data_marketplace.cog.async_tiff_parser import AsyncTiffStacCogProcessor
            self.cog_processor = AsyncTiffStacCogProcessor()

            self.stac_processor = StreamingStacProcessor(
                self.unified_schema,
                self.cog_processor,
                existing_key_checker=None,
                io_optimized=True,  # Enable aggressive I/O optimization
            )

        logger.info(f"Starting STAC ingestion for collection: {collection_id}")
        logger.info(f"Max items: {max_items}, Batch size: {batch_size}")
        logger.info(f"Concurrency: {max_concurrent_cog_requests} COG requests, {max_concurrent_stac_items} STAC items")
        logger.info(f"Write mode: {'MERGE (force reingest)' if force_reingest else 'APPEND (normal)'}")
        logger.info(f"Optimization mode: {'I/O-optimized (aggressive)' if io_optimized else 'Auto-tuned (conservative)'}")

        try:
            # Connect to STAC API (best-effort; allow tests to inject a mock processor)
            search_items_iter = None
            try:
                catalog = pystac_client.Client.open(stac_api_url)
            except Exception as e:
                logger.warning(
                    f"STAC API open failed ({e}); proceeding with empty iterator (processor may be mocked)"
                )
                catalog = None

            # Fast date-based completeness check - skip if force reingest is enabled or if explicitly skipped
            if datetime_range and catalog is not None and not force_reingest and not _skip_fast_check:
                logger.info(
                    f"🚀 Fast date-based completeness check for range: {datetime_range}"
                )

                # Use new SimpleDateChecker for fast count-based checking
                date_checker = SimpleDateChecker(self.unified_table_path, self.storage_options)
                missing_dates = date_checker.get_dates_needing_ingestion(
                    catalog, collection_id, datetime_range
                )

                if not missing_dates:
                    logger.info(
                        f"✅ All dates have matching counts for range {datetime_range} - skipping ingestion"
                    )
                    # Return empty stats since no work was needed
                    end_time = datetime.now()
                    duration = (end_time - start_time).total_seconds()
                    stats["stac_items_processed"] = 0
                    stats["unified_records_written"] = 0
                    stats["duration_seconds"] = duration
                    stats["performance"] = {
                        "items_per_second": 0.0,
                        "unified_records_per_second": 0.0,
                    }
                    stats["message"] = (
                        "All dates have matching counts - no ingestion needed"
                    )
                    return stats
                else:
                    logger.info(
                        f"🎯 Found {len(missing_dates)} dates needing ingestion - proceeding with targeted date-based ingestion"
                    )
                    # Ingest each missing date individually with APPEND mode
                    return await self._ingest_missing_dates(
                        missing_dates, stac_api_url, collection_id, bbox, max_items, start_time,
                        batch_size, max_concurrent_cog_requests, max_concurrent_stac_items,
                        optimize_every_n_batches, vacuum_retention_hours, skip_vacuum, skip_optimize, io_optimized
                    )

            # Build search parameters
            search_params = {"collections": [collection_id]}
            if datetime_range:
                search_params["datetime"] = datetime_range
            if bbox:
                search_params["bbox"] = bbox
            if max_items:
                search_params["max_items"] = max_items

            # Prefetch existing (scene_id -> cog_key set) once by default when a datetime_range is provided
            # Check if prefetch was already done during pre-check to avoid double work
            if datetime_range:
                try:
                    if (
                        hasattr(self.restart_manager, "_prefetch_map")
                        and self.restart_manager._prefetch_map
                    ):
                        # Reuse existing prefetch from pre-check
                        pre_map = self.restart_manager._prefetch_map
                        logger.info(
                            f"Reusing existing keys prefetch for range {datetime_range}: {len(pre_map)} scenes with prior rows"
                        )
                    else:
                        # Do fresh prefetch if not already done
                        pre_map = (
                            self.restart_manager.prefetch_existing_cog_keys_for_range(
                                datetime_range, bbox
                            )
                        )
                        logger.info(
                            f"Existing keys prefetch enabled for range {datetime_range}: {len(pre_map)} scenes with prior rows"
                        )

                    # Bind a dict-backed checker into the processor for O(1) lookups
                    existing_key_checker = lambda sid, _: pre_map.get(sid, set())
                    self.stac_processor.existing_key_checker = existing_key_checker
                    # CRITICAL: Also set it on the producer for per-item filtering
                    self.stac_processor.producer.existing_key_checker = existing_key_checker
                    logger.info(f"🔑 Set existing_key_checker on processor and producer with {len(pre_map)} scenes")
                except Exception as e:
                    logger.warning(
                        f"Prefetch failed; proceeding without existing-key filtering: {e}"
                    )
                    self.stac_processor.existing_key_checker = None
                    # CRITICAL: Also clear it on the producer
                    self.stac_processor.producer.existing_key_checker = None

            logger.info(f"STAC search parameters: {search_params}")

            # Search for items using standard date-range search
            if catalog is not None:
                logger.info(f"📅 Using date-range search for all items in range")
                search = catalog.search(**search_params)
                search_items_iter = search.items()
            else:
                search_items_iter = iter([])

            # Pass STAC API URL to processor for federation metadata
            if self.stac_api_url:
                self.stac_processor.stac_api_url = self.stac_api_url

            # Memory baseline
            self._log_memory("before_batches")

            # Process items in batches using streaming processor
            batch_count = 0
            processing_method = self.stac_processor.process_stac_items_in_batches(
                search_items_iter,
                batch_size,
                max_concurrent_cog_requests,
                max_concurrent_stac_items,
            )

            async for batch_stats, unified_records in processing_method:
                # Write unified records using DeltaManager
                if unified_records:
                    try:
                        # Minimal normalization for tests/mocks: ensure required fields and types
                        normalized_records = []
                        for rec in unified_records:
                            r = dict(rec)
                            if "collection" not in r and "collection_id" in r:
                                r["collection"] = r["collection_id"]
                            # Ensure datetime is iso string parseable by schema
                            if isinstance(r.get("datetime"), str):
                                # leave as-is; schema parser accepts ISO strings
                                pass
                            # Optional: ensure geometry/bbox presence even if None
                            r.setdefault("geometry", None)
                            r.setdefault("bbox", None)
                            normalized_records.append(r)

                        unified_table = self.unified_schema.create_pyarrow_table(
                            normalized_records
                        )
                        records_written = (
                            await self._write_unified_records_with_delta_manager(
                                unified_table, force_reingest=force_reingest
                            )
                        )
                        batch_stats["unified_records_written"] = records_written
                    except Exception as e:
                        logger.error(f"❌ Error creating PyArrow table: {e}")
                        batch_stats["unified_records_written"] = 0
                    finally:
                        # Proactively free large local references to help GC between batches
                        try:
                            del unified_records
                            del normalized_records
                            del unified_table
                        except Exception:
                            pass

                # Update statistics
                stats["stac_items_processed"] += batch_stats["stac_items_processed"]
                stats["cog_assets_processed"] += batch_stats.get(
                    "cog_assets_processed", 0
                )
                stats["unified_records_written"] += batch_stats[
                    "unified_records_written"
                ]
                stats["errors"].extend(batch_stats["errors"])

                # Simple progress logging
                batch_count += 1
                logger.info(
                    f"✅ Batch {batch_count} complete: {batch_stats['stac_items_processed']} items → "
                    f"{batch_stats['unified_records_written']} records written "
                    f"(Total: {stats['stac_items_processed']} items, {stats['unified_records_written']} records)"
                )

                # Log memory after each batch and free references
                self._log_memory(f"after_batch_{batch_count}")

                # Optimize every N batches
                if batch_count % optimize_every_n_batches == 0:
                    logger.info(
                        f"🔧 Running optimization after {batch_count} batches..."
                    )
                    # Best effort compact; ignore failures to avoid halting ingestion
                    try:
                        self.delta_manager.optimize_table("unified_stac_table")
                    except Exception as opt_e:
                        logger.debug(f"Optimize skipped: {opt_e}")

                # Periodically clear small caches to avoid unbounded growth
                if batch_count % 10 == 0:
                    try:
                        self.stac_processor.clear_existing_keys_cache()
                    except Exception:
                        pass

                # Break if we've hit max_items (streaming processor pre-processes all items)
                if max_items and stats["stac_items_processed"] >= max_items:
                    # Note: streaming processor may have already processed more items than max_items
                    # due to its producer-consumer architecture
                    break

        except Exception as e:
            logger.error(f"Error during STAC ingestion: {e}", exc_info=True)
            stats["errors"].append({"type": "ingestion_error", "message": str(e)})

        # Calculate final statistics
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()

        stats.update(
            {
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "performance": {
                    "items_per_second": (
                        stats["stac_items_processed"] / duration if duration > 0 else 0
                    ),
                    "cog_assets_per_second": (
                        stats["cog_assets_processed"] / duration if duration > 0 else 0
                    ),
                    "unified_records_per_second": (
                        stats["unified_records_written"] / duration
                        if duration > 0
                        else 0
                    ),
                    "total_records_written": stats["unified_records_written"],
                },
            }
        )

        logger.info(f"Unified STAC ingestion completed in {duration:.2f}s")
        logger.info(
            f"Performance: {stats['performance']['items_per_second']:.2f} items/sec"
        )
        logger.info(
            f"Total unified records written: {stats['performance']['total_records_written']}"
        )

        # Final table cleanup and optimization
        await self._perform_table_cleanup(
            skip_vacuum=skip_vacuum,
            skip_optimize=skip_optimize,
            vacuum_retention_hours=vacuum_retention_hours
        )

        # Get total table row count for final statistics
        total_table_rows = self._get_total_table_rows()
        if total_table_rows > 0:
            logger.info(f"📊 Total table rows after ingestion: {total_table_rows:,}")
            stats["performance"]["total_table_rows"] = total_table_rows

        return stats

    async def _ingest_missing_dates(
        self,
        missing_dates: List[str],
        stac_api_url: str,
        collection_id: str,
        bbox: Optional[List[float]] = None,
        max_items: Optional[int] = None,
        start_time: datetime = None,
        batch_size: int = 2000,
        max_concurrent_cog_requests: Optional[int] = None,
        max_concurrent_stac_items: Optional[int] = None,
        optimize_every_n_batches: int = 100,
        vacuum_retention_hours: int = 168,
        skip_vacuum: bool = False,
        skip_optimize: bool = False,
        io_optimized: bool = False,
    ) -> Dict[str, Any]:
        """
        Ingest specific dates with MERGE mode for proper duplicate handling.

        This method processes each missing date individually to ensure
        existing COG keys are properly handled via MERGE mode.
        """
        logger.info(f"🎯 Starting targeted date-based ingestion for {len(missing_dates)} dates")

        # Initialize combined stats
        combined_stats = {
            "stac_items_processed": 0,
            "unified_records_written": 0,
            "duration_seconds": 0.0,
            "performance": {
                "items_per_second": 0.0,
                "unified_records_per_second": 0.0,
            },
            "dates_processed": [],
            "errors": [],  # Add missing errors key
            "message": f"Targeted ingestion of {len(missing_dates)} dates with MERGE mode"
        }

        successful_dates = 0
        failed_dates = []

        for i, date_str in enumerate(missing_dates, 1):
            try:
                logger.info(f"📅 Processing date {i}/{len(missing_dates)}: {date_str}")

                # Create datetime range for this specific date
                date_range = f"{date_str}T00:00:00/{date_str}T23:59:59"

                # Ingest this date with APPEND mode (pre-filtering handles duplicates)
                date_stats = await self.ingest_stac_collection(
                    stac_api_url=stac_api_url,
                    collection_id=collection_id,
                    datetime_range=date_range,
                    bbox=bbox,
                    max_items=max_items,
                    batch_size=batch_size,  # Pass through the original batch size
                    max_concurrent_cog_requests=max_concurrent_cog_requests,
                    max_concurrent_stac_items=max_concurrent_stac_items,
                    optimize_every_n_batches=optimize_every_n_batches,
                    force_reingest=False,  # Use APPEND mode - pre-filtering handles duplicates
                    vacuum_retention_hours=vacuum_retention_hours,
                    skip_vacuum=skip_vacuum,
                    skip_optimize=skip_optimize,
                    io_optimized=io_optimized,
                    # Disable the fast check for individual dates to avoid recursion
                    _skip_fast_check=True
                )

                # Accumulate stats
                combined_stats["stac_items_processed"] += date_stats.get("stac_items_processed", 0)
                combined_stats["unified_records_written"] += date_stats.get("unified_records_written", 0)
                combined_stats["dates_processed"].append(date_str)

                # Accumulate errors from individual date ingestions
                date_errors = date_stats.get("errors", [])
                if date_errors:
                    combined_stats["errors"].extend(date_errors)

                successful_dates += 1

                logger.info(f"✅ Date {date_str} completed: {date_stats.get('stac_items_processed', 0)} items, {date_stats.get('unified_records_written', 0)} records")

            except Exception as e:
                error_msg = f"Failed to ingest date {date_str}: {e}"
                logger.error(f"❌ {error_msg}")
                combined_stats["errors"].append(error_msg)
                failed_dates.append(date_str)
                continue

        # Calculate final stats
        if start_time:
            end_time = datetime.now()
            combined_stats["duration_seconds"] = (end_time - start_time).total_seconds()

            if combined_stats["duration_seconds"] > 0:
                combined_stats["performance"]["items_per_second"] = (
                    combined_stats["stac_items_processed"] / combined_stats["duration_seconds"]
                )
                combined_stats["performance"]["unified_records_per_second"] = (
                    combined_stats["unified_records_written"] / combined_stats["duration_seconds"]
                )

        # Update message with results
        if failed_dates:
            combined_stats["message"] += f" - {successful_dates}/{len(missing_dates)} dates successful, {len(failed_dates)} failed"
            combined_stats["failed_dates"] = failed_dates
        else:
            combined_stats["message"] += f" - all {successful_dates} dates successful"

        logger.info(f"🎯 Targeted date-based ingestion completed: {successful_dates}/{len(missing_dates)} dates successful")

        return combined_stats

    def _get_total_table_rows(self) -> int:
        """Get total row count from Delta table metadata (fast operation)."""
        try:
            from deltalake import DeltaTable

            # Use cached DeltaTable if available, otherwise create new one
            dt = DeltaTable(self.unified_table_path, storage_options=self.storage_options)

            # Get row count from Delta Lake metadata (very fast - no data scan)
            # This uses the transaction log metadata, not a full table scan
            files = dt.files()
            if not files:
                return 0

            # Use DuckDB for fast row count from Delta metadata
            try:
                import duckdb
                conn = duckdb.connect()
                conn.execute("INSTALL delta")
                conn.execute("LOAD delta")

                # Configure DuckDB for 7GB RAM environment
                conn.execute("SET memory_limit='5.5GB'")
                conn.execute("SET threads=2")
                conn.execute("SET preserve_insertion_order=false")

                # Configure S3 credentials if needed
                if self.unified_table_path.startswith("s3://") and self.storage_options:
                    if "AWS_ACCESS_KEY_ID" in self.storage_options:
                        conn.execute(f"""
                            CREATE SECRET s3_secret (
                                TYPE S3,
                                KEY_ID '{self.storage_options["AWS_ACCESS_KEY_ID"]}',
                                SECRET '{self.storage_options["AWS_SECRET_ACCESS_KEY"]}',
                                REGION '{self.storage_options.get("AWS_REGION", "us-west-2")}'
                            )
                        """)
                    else:
                        conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")

                # Fast count using Delta metadata
                result = conn.execute(f"SELECT count(*) FROM delta_scan('{self.unified_table_path}')").fetchone()
                return int(result[0]) if result and result[0] is not None else 0

            except Exception as e:
                logger.debug(f"Could not get row count via DuckDB: {e}")
                # Fallback: estimate from file count (less accurate but fast)
                return len(files) * 1000  # Rough estimate

        except Exception as e:
            logger.debug(f"Could not get total table rows: {e}")
            return 0

    async def _perform_table_cleanup(
        self,
        skip_vacuum: bool = False,
        skip_optimize: bool = False,
        vacuum_retention_hours: int = 168
    ) -> None:
        """
        Perform table cleanup operations: vacuum and optimize.

        Args:
            skip_vacuum: Skip vacuum cleanup of old/orphan files
            skip_optimize: Skip table optimization/compaction
            vacuum_retention_hours: Hours to retain old files before vacuum cleanup
        """
        try:
            from deltalake import DeltaTable

            # Load the Delta table for cleanup operations
            dt = DeltaTable(self.unified_table_path, storage_options=self.storage_options, log_buffer_size=4)

            # 1. Optimize/Compact the table (combine small files)
            if not skip_optimize:
                logger.info("🔧 Optimizing table (compacting small files)...")
                try:
                    optimize_result = dt.optimize.compact()
                    files_added = optimize_result.get('files_added', 0)
                    files_removed = optimize_result.get('files_removed', 0)
                    logger.info(f"✅ Table optimized: {files_removed} small files → {files_added} optimized files")
                except Exception as e:
                    logger.warning(f"⚠️  Table optimization failed: {e}")
            else:
                logger.info("⏭️  Skipping table optimization")

            # 2. Vacuum old and orphan files
            if not skip_vacuum:
                logger.info(f"🧹 Vacuuming old files (retention: {vacuum_retention_hours} hours)...")
                try:
                    # Enforce minimum safe retention (7 days = 168 hours)
                    safe_retention_hours = max(vacuum_retention_hours, 168)
                    if vacuum_retention_hours < 168:
                        logger.warning(f"⚠️  Vacuum retention increased from {vacuum_retention_hours}h to {safe_retention_hours}h for safety")

                    # First do a dry run to see what would be deleted
                    dry_run_files = dt.vacuum(
                        retention_hours=safe_retention_hours,
                        dry_run=True
                    )
                    logger.info(f"📋 Found {len(dry_run_files)} old/orphan files to clean up")

                    if dry_run_files:
                        # Log some examples of files to be deleted for transparency
                        example_files = dry_run_files[:3]  # Show first 3 files
                        logger.info(f"📋 Example files to delete: {example_files}")

                        # Actually delete the files
                        deleted_files = dt.vacuum(
                            retention_hours=safe_retention_hours,
                            dry_run=False
                        )
                        logger.info(f"✅ Vacuum completed: {len(deleted_files)} old/orphan files deleted")
                    else:
                        logger.info("✅ No old/orphan files to clean up")
                except Exception as e:
                    logger.warning(f"⚠️  Vacuum operation failed: {e}")
            else:
                logger.info("⏭️  Skipping vacuum cleanup")

        except Exception as e:
            logger.error(f"❌ Table cleanup failed: {e}")
            # Don't raise - cleanup failures shouldn't stop the ingestion

    # Optimization method moved to DeltaManager

    async def _write_unified_records_with_delta_manager(
        self, unified_table: pa.Table, force_reingest: bool = False
    ) -> int:
        """Write unified records using DeltaManager for MERGE deduplication."""
        try:
            logger.info(f"🔄 Starting Delta write to: {self.unified_table_path}")
            logger.info(
                f"📊 Table info: {len(unified_table)} rows, {len(unified_table.columns)} columns"
            )
            logger.info(f"🔑 Storage options: {self.storage_options}")

            # Create writer properties
            # Configure Bloom filters only for selected columns (avoid enabling for all columns)
            bloom_cols = (
                list(self.unified_schema.get_bloom_filter_columns())
                if hasattr(self.unified_schema, "get_bloom_filter_columns")
                else []
            )
            col_props = None
            if bloom_cols:
                col_props = {
                    col: ColumnProperties(
                        bloom_filter_properties=BloomFilterProperties(True, 0.1, 1000)
                    )
                    for col in bloom_cols
                }

            writer_properties = WriterProperties(
                data_page_size_limit=self.parquet_settings["data_page_size"],
                max_row_group_size=self.parquet_settings["row_group_size"],
                column_properties=col_props,
            )

            # Use explicit output path URI so S3 writes go where the user requested
            # Check if we can use fast APPEND mode (when pre-filtering is active and not force reingest)
            # Pre-filtering is active if we have a prefetch map (even if empty - means we checked)
            use_append = (
                not force_reingest and
                hasattr(self.restart_manager, "_prefetch_map")
                and self.restart_manager._prefetch_map is not None
            )
            if use_append:
                logger.info(
                    "⚡ Using fast APPEND mode (pre-filtering active - no duplicates expected)"
                )
            else:
                mode_reason = "force reingest enabled" if force_reingest else "no pre-filtering - duplicates possible"
                logger.info(
                    f"🔄 Using MERGE mode ({mode_reason})"
                )

            logger.info(
                "�🚀 Calling DeltaManager.write_with_merge_deduplication_uri..."
            )
            self.delta_manager.write_with_merge_deduplication_uri(
                table_uri=self.unified_table_path,
                data=unified_table,
                merge_key=[
                    "scene_id",
                    "cog_key",
                ],  # Use composite key for proper deduplication
                partition_by=["year", "month"],
                writer_properties=writer_properties,
                storage_options=self.storage_options,
                force_append=use_append,  # Use APPEND when pre-filtering is active
            )

            logger.info(
                f"💾 Delta write complete: {len(unified_table)} records written to {self.unified_table_path}"
            )
            return len(unified_table)

        except Exception as e:
            logger.error(
                f"Error writing unified records with DeltaManager: {e}", exc_info=True
            )
            return 0

    def get_table_info(self) -> Dict[str, Any]:
        """Get information about the unified Delta Lake table."""
        info = {
            "unified_table": {
                "path": self.unified_table_path,
                "exists": False,
                "version": None,
                "num_files": 0,
            }
        }

        try:
            # Check unified table
            if Path(self.unified_table_path).exists():
                unified_dt = DeltaTable(
                    self.unified_table_path, storage_options=self.storage_options
                )
                info["unified_table"].update(
                    {
                        "exists": True,
                        "version": unified_dt.version(),
                        "num_files": len(unified_dt.files()),
                        "schema": str(unified_dt.schema()),
                    }
                )
        except Exception as e:
            logger.debug(f"Could not read unified table info: {e}")

        return info

# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Compatibility shim exposing StacProcessor interface for tests.

Delegates to the modular streaming components to avoid duplication and keep
code slim while preserving expected behavior in unit tests that import
`data_marketplace.ingestion.stac_processor.StacProcessor`.
"""
from __future__ import annotations

from typing import Any, Dict, List, Optional, AsyncIterator, Tuple

from data_marketplace.ingestion.stac_schema import UnifiedStacSchema
from data_marketplace.cog.stac_cog_processor import StacCogProcessor
from data_marketplace.ingestion.streaming import StreamingProducer, StreamingStats
from data_marketplace.ingestion.streaming_processor import StreamingStacProcessor


class StacProcessor:
    """
    Thin wrapper that provides:
    - create_unified_records_for_item
    - process_stac_items_in_batches (delegates to StreamingStacProcessor)
    """

    def __init__(
        self,
        unified_schema: UnifiedStacSchema,
        cog_processor: StacCogProcessor,
        existing_key_checker: Optional[callable] = None,
    ) -> None:
        self.unified_schema = unified_schema
        self.cog_processor = cog_processor
        self.existing_key_checker = existing_key_checker

        # A lightweight stats object for compatibility
        self._stats = StreamingStats()

        # Reuse StreamingProducer logic (no duplication) - always use fast parser
        self._producer = StreamingProducer(
            unified_schema=self.unified_schema,
            cog_processor=self.cog_processor,
            stats=self._stats,
            existing_key_checker=self.existing_key_checker,
        )

        # Orchestrator for batch processing
        self._orchestrator = StreamingStacProcessor(
            unified_schema=self.unified_schema,
            cog_processor=self.cog_processor,
            existing_key_checker=self.existing_key_checker,
            use_fast_parser=self.use_fast_parser,
        )

    async def create_unified_records_for_item(
        self, stac_item: Any, max_concurrent_requests: int
    ) -> List[Dict[str, Any]]:
        """
        Create unified records for a single STAC item using the streaming producer.
        """
        recs = await self._producer.create_unified_records_for_item(
            stac_item, max_concurrent_requests
        )
        # Back-compat: ensure cog_key/cog_href when tests only return asset_* fields
        out: List[Dict[str, Any]] = []
        for r in recs:
            if "cog_key" not in r and "asset_key" in r:
                r = {**r, "cog_key": r["asset_key"]}
            if "cog_href" not in r and "asset_href" in r:
                r = {**r, "cog_href": r["asset_href"]}
            out.append(r)
        return out

    async def process_stac_items_in_batches(
        self,
        stac_items: AsyncIterator,
        batch_size: int,
        max_concurrent_cog_requests: int = 200,
        max_concurrent_stac_items: int = 20,
    ) -> AsyncIterator[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
        """
        Delegate to StreamingStacProcessor to preserve legacy test interface.
        """
        # align stac_api_url passthrough if set by callers
        if hasattr(self, "stac_api_url") and self.stac_api_url:
            self._orchestrator.stac_api_url = self.stac_api_url
        async for batch in self._orchestrator.process_stac_items_in_batches(
            stac_items,
            batch_size,
            max_concurrent_cog_requests,
            max_concurrent_stac_items,
        ):
            yield batch

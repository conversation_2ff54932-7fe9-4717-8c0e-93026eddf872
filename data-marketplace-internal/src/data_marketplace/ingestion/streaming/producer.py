# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Producer component for streaming STAC processing."""

import logging
from typing import Dict, Any, List, Optional, Union, Iterator, AsyncIterator
from anyio.streams.memory import MemoryObjectSendStream

from data_marketplace.ingestion.streaming.types import StreamingStats, CogWorkItem
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema
from data_marketplace.cog.stac_cog_processor import StacCogProcessor

logger = logging.getLogger(__name__)


class StreamingProducer:
    """
    Producer component that converts STAC items into unified records.

    This component handles:
    - STAC item iteration (async or sync)
    - COG asset filtering and existing key checking
    - Unified record creation
    - Error handling and statistics
    """

    def __init__(
        self,
        unified_schema: UnifiedStacSchema,
        cog_processor: StacCogProcessor,
        stats: StreamingStats,
        existing_key_checker: Optional[callable] = None,
        use_fast_parser: bool = True,
    ):
        self.unified_schema = unified_schema
        self.cog_processor = cog_processor
        self.stats = stats
        self.existing_key_checker = existing_key_checker
        self.use_fast_parser = use_fast_parser

        # Cache for existing keys to avoid repeated table scans
        self._existing_keys_cache = {}

        # For compatibility with existing code
        self.stac_api_url = None

    async def produce(
        self,
        send: MemoryObjectSendStream,
        stac_items: Union[Iterator, AsyncIterator],
        max_concurrent_cog_requests: int = 200,
        max_concurrent_stac_items: int = 20,
    ):
        """
        Produce unified records from STAC items.

        Args:
            send: Stream to send unified records to
            stac_items: Iterator of STAC items (async or sync)
            max_concurrent_cog_requests: Max concurrent COG requests
            max_concurrent_stac_items: Max concurrent STAC items
        """
        logger.debug("Starting streaming producer")
        async with send:
            # Handle both async and regular iterators
            if hasattr(stac_items, "__aiter__"):
                # Async iterator
                async for stac_item in stac_items:
                    try:
                        await self._process_stac_item(
                            stac_item, send, max_concurrent_cog_requests
                        )
                    except Exception as e:
                        logger.error(f"Producer error processing STAC item: {e}")
                        self.stats.update(errors=1)
            else:
                # Regular iterator
                for stac_item in stac_items:
                    try:
                        await self._process_stac_item(
                            stac_item, send, max_concurrent_cog_requests
                        )
                    except Exception as e:
                        logger.error(f"Producer error processing STAC item: {e}")
                        self.stats.update(errors=1)
        logger.debug("Producer finished")

    async def _process_stac_item(
        self,
        stac_item,
        send: MemoryObjectSendStream,
        max_concurrent_requests: int,
    ):
        """Process a single STAC item and send COG-level work items."""
        try:
            item_dict = (
                stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item
            )
            base_scene = self.unified_schema.flatten_stac_item(item_dict)
            scene_id = (
                base_scene.get("scene_id")
                or base_scene.get("id")
                or getattr(stac_item, "id", None)
            )

            # Determine which assets to process (respect existing key checker)
            only_keys = await self._get_missing_cog_keys(stac_item)

            # Loop assets and enqueue CogWorkItems for true COG-level parallelism
            assets = getattr(stac_item, "assets", {})
            for asset_key, asset in assets.items():
                if only_keys is not None and asset_key not in only_keys:
                    continue
                if not self.cog_processor.is_cog_asset(asset):
                    continue

                work_item = CogWorkItem(
                    stac_item=item_dict,
                    asset_key=asset_key,
                    asset_href=str(getattr(asset, "href", "")),
                    asset_data=(
                        asset.to_dict()
                        if hasattr(asset, "to_dict")
                        else getattr(asset, "extra_fields", {})
                    ),
                    base_scene=base_scene,
                    scene_id=scene_id or "unknown",
                )
                await send.send(work_item)

            # Update statistics per item enqueued (records created updated by consumers)
            self.stats.update(items=1, records=0)

        except Exception as e:
            logger.error(f"Error processing STAC item: {e}")
            self.stats.update(errors=1)

    async def create_unified_records_for_item(
        self,
        stac_item,
        max_concurrent_requests: int,
    ) -> List[Dict[str, Any]]:
        """
        Create unified records for a STAC item.

        This method handles COG filtering, existing key checking, and unified record creation.
        """
        unified_records = []

        try:
            # Handle existing key checking and COG filtering
            only_keys = await self._get_missing_cog_keys(stac_item)

            # If only_keys is an empty set, skip processing
            if only_keys is not None and len(only_keys) == 0:
                return unified_records

            # Parse COG headers using appropriate parser
            cog_records = await self._parse_cog_headers(
                stac_item, max_concurrent_requests, only_keys
            )

            # Create unified records
            unified_records = await self._create_unified_records(stac_item, cog_records)

        except Exception as e:
            logger.error(f"Error creating unified records for STAC item: {e}")

        return unified_records

    async def _get_missing_cog_keys(self, stac_item) -> Optional[set]:
        """Get missing COG keys for a STAC item using existing key checker."""
        only_keys = None
        try:
            if self.existing_key_checker is not None:
                scene_id = getattr(stac_item, "id", None)
                collection = getattr(stac_item, "collection_id", None)

                if scene_id:
                    cache_key = (scene_id, collection)
                    if cache_key in self._existing_keys_cache:
                        existing = self._existing_keys_cache[cache_key]
                    else:
                        existing = self.existing_key_checker(scene_id, collection)
                        self._existing_keys_cache[cache_key] = set(existing or set())

                    if existing is not None:
                        # Get candidate COG keys from item assets
                        candidate_keys = {
                            key
                            for key, asset in getattr(stac_item, "assets", {}).items()
                            if self.cog_processor.is_cog_asset(asset)
                        }
                        # Compute missing keys
                        missing = candidate_keys - set(existing)
                        only_keys = missing if missing else set()

                        # DEBUG: Log filtering results
                        if len(missing) == 0:
                            logger.debug(f"🔍 Scene {scene_id}: All {len(candidate_keys)} COGs exist - SKIPPING")
                        else:
                            logger.debug(f"🔍 Scene {scene_id}: {len(missing)}/{len(candidate_keys)} COGs missing - PROCESSING")
            else:
                logger.debug(f"🔍 No existing_key_checker - processing all COGs for scene")
        except Exception as e:
            logger.debug(f"existing_key_checker failed, proceeding without filter: {e}")
            only_keys = None

        return only_keys

    async def _parse_cog_headers(
        self, stac_item, max_concurrent_requests: int, only_keys: Optional[set]
    ):
        """Parse COG headers using appropriate parser."""
        if self.use_fast_parser and hasattr(
            self.cog_processor, "parse_cog_headers_for_item_fast"
        ):
            return await self.cog_processor.parse_cog_headers_for_item_fast(
                stac_item, max_concurrent_requests, only_keys=only_keys
            )
        else:
            # Backward-compatible call with fallback
            try:
                return await self.cog_processor.parse_cog_headers_for_item(
                    stac_item, max_concurrent_requests, only_keys=only_keys
                )
            except TypeError:
                # Fallback to legacy signature
                return await self.cog_processor.parse_cog_headers_for_item(
                    stac_item, max_concurrent_requests
                )

    async def _create_unified_records(
        self, stac_item, cog_records: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """Create unified records from STAC item and COG records."""
        unified_records = []

        # Flatten STAC item once for efficiency
        item_dict = stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item
        base_scene = self.unified_schema.flatten_stac_item(item_dict)

        # Add federation metadata if available
        if self.stac_api_url:
            base_scene["stac_api_url"] = self.stac_api_url

        # Create one unified record per COG asset
        for cog_record in cog_records:
            unified_record = {**base_scene, **cog_record}
            unified_records.append(unified_record)

        return unified_records

    def clear_existing_keys_cache(self):
        """Clear the existing keys cache."""
        self._existing_keys_cache.clear()

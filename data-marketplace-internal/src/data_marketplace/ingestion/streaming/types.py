# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Data types for streaming STAC processing."""

import time
from typing import Dict, Any
from dataclasses import dataclass, field


@dataclass
class CogWorkItem:
    """
    Individual COG processing work item - inspired by tiff-dumper architecture.

    This represents a single COG asset to be processed, allowing true
    COG-level parallelism in the streaming processor.
    """

    stac_item: Dict[str, Any]  # Full STAC item
    asset_key: str  # COG asset key (e.g., 'red', 'blue')
    asset_href: str  # COG URL
    asset_data: Dict[str, Any]  # Asset metadata
    base_scene: Dict[str, Any]  # Pre-flattened STAC metadata
    scene_id: str  # STAC item ID


@dataclass
class StreamingStats:
    """Real-time streaming statistics."""

    items_processed: int = 0
    records_created: int = 0
    errors: int = 0
    start_time: float = field(default_factory=time.time)
    last_update: float = field(default_factory=time.time)

    @property
    def elapsed_time(self) -> float:
        return time.time() - self.start_time

    @property
    def items_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.items_processed / self.elapsed_time
        return 0.0

    @property
    def records_per_second(self) -> float:
        if self.elapsed_time > 0:
            return self.records_created / self.elapsed_time
        return 0.0

    def update(self, items: int = 0, records: int = 0, errors: int = 0):
        """Update statistics."""
        self.items_processed += items
        self.records_created += records
        self.errors += errors
        self.last_update = time.time()

    def to_dict(self) -> Dict[str, Any]:
        """Convert stats to dictionary for logging/reporting."""
        return {
            "items_processed": self.items_processed,
            "records_created": self.records_created,
            "errors": self.errors,
            "elapsed_time": self.elapsed_time,
            "items_per_second": self.items_per_second,
            "records_per_second": self.records_per_second,
        }

# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Streaming STAC processor using modular producer-consumer architecture.

This module orchestrates the streaming components for high-throughput STAC processing:
- Producer: Converts STAC items to unified records
- Consumers: Process records (currently pass-through)
- Collector: Batches records for output

Maintains exact compatibility with existing processor interface while providing
better maintainability through modular design.
"""

import logging
from typing import Dict, Any, List, Optional, AsyncIterator, Tuple

from anyio import create_memory_object_stream, create_task_group

from data_marketplace.ingestion.stac_schema import UnifiedStacSchema
from data_marketplace.cog.stac_cog_processor import StacCogProcessor
from data_marketplace.cog.cog_parser import COGHeaderParser
from data_marketplace.ingestion.streaming import (
    StreamingStats,
    StreamingProducer,
    StreamingConsumer,
    StreamingCollector,
)

logger = logging.getLogger(__name__)


class StreamingStacProcessor:
    """
    Streaming STAC processor using modular producer-consumer architecture.

    This processor orchestrates streaming components to provide high-throughput
    STAC processing while maintaining exact compatibility with the existing
    processor interface.
    """

    def __init__(
        self,
        unified_schema: UnifiedStacSchema,
        cog_processor: StacCogProcessor,
        existing_key_checker: Optional[callable] = None,
        max_buffer_size: Optional[int] = None,  # Auto-tuned based on CPU cores
        n_consumers: Optional[int] = None,  # Auto-tuned based on CPU cores
        monitoring_interval: float = 5.0,  # Monitor every 5 seconds
        io_optimized: bool = False,  # Use aggressive I/O-optimized settings
    ):
        """
        Initialize streaming STAC processor with auto-tuned performance parameters.

        Args:
            unified_schema: Schema for unified STAC + COG records
            cog_processor: COG header processor
            existing_key_checker: Function to check for existing COG keys
            max_buffer_size: Maximum items in memory streams (auto-tuned if None)
            n_consumers: Number of consumer tasks (auto-tuned if None)
            monitoring_interval: Seconds between monitoring updates
        """
        import os

        self.unified_schema = unified_schema
        self.cog_processor = cog_processor
        self.existing_key_checker = existing_key_checker

        # Auto-tune parameters based on CPU cores and I/O optimization mode
        cpu_count = os.cpu_count() or 4  # Fallback to 4 if detection fails
        batch_size = 16  # Natural STAC item size (Sentinel-2 has ~16 COG assets)

        if io_optimized:
            # Aggressive I/O-optimized mode (respecting file descriptor limits)
            self.n_consumers = n_consumers if n_consumers is not None else min(500, cpu_count * 125)
            self.max_buffer_size = max_buffer_size if max_buffer_size is not None else min(1000000, cpu_count * 25000)
            logger.info(f"I/O-optimized mode: {self.n_consumers} consumers, {self.max_buffer_size} buffer size")
        else:
            # Conservative auto-tuning
            self.n_consumers = n_consumers if n_consumers is not None else cpu_count
            self.max_buffer_size = max_buffer_size if max_buffer_size is not None else (batch_size * self.n_consumers * 2)

        # Other proven optimal settings
        self.output_batch_size = 4000  # Proven optimal for Delta Lake writes
        self.queue_size = 10  # Sufficient backpressure control
        self.batch_size = batch_size  # Store for consumer configuration

        self.monitoring_interval = monitoring_interval

        # Initialize streaming components
        self.stats = StreamingStats()
        self.producer = StreamingProducer(
            unified_schema, cog_processor, self.stats, existing_key_checker
        )
        self.collector = StreamingCollector(self.stats)

        # For compatibility with existing code
        self.stac_api_url = None

        logger.info(
            f"Auto-tuned StreamingStacProcessor: {cpu_count} CPU cores → {self.n_consumers} consumers, buffer size {self.max_buffer_size}, batch size {self.batch_size}"
        )

    async def process_stac_items_in_batches(
        self,
        stac_items: AsyncIterator,
        batch_size: int,
        max_concurrent_cog_requests: int = 200,
        max_concurrent_stac_items: int = 20,
    ) -> AsyncIterator[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
        """
        Process STAC items using modular streaming architecture.

        This implementation uses modular components:
        - Producer: Converts STAC items to unified records
        - Consumers: Process records (currently pass-through)
        - Collector: Batches records for output

        Args:
            stac_items: Iterator of STAC items to process
            batch_size: Number of records per output batch
            max_concurrent_cog_requests: Max concurrent COG header requests
            max_concurrent_stac_items: Max concurrent STAC items

        Yields:
            Tuple of (batch_stats, unified_records) for each batch
        """
        # Set up producer API URL for federation metadata
        if hasattr(self, "stac_api_url") and self.stac_api_url:
            self.producer.stac_api_url = self.stac_api_url

        # Use provided batch_size if specified; otherwise fall back to configured output_batch_size
        output_batch_size = batch_size or getattr(self, "output_batch_size", 4000)

        logger.debug(
            f"Starting modular streaming with {self.n_consumers} consumers, batch size {output_batch_size}"
        )

        # Create memory streams for producer -> consumers -> collector
        send_to_consumers, receive_from_producer = create_memory_object_stream(
            self.max_buffer_size, item_type=dict
        )
        send_to_collector, receive_from_consumers = create_memory_object_stream(
            self.max_buffer_size, item_type=dict
        )

        # Auto-tune max concurrency based on CPU cores (first principles)
        if max_concurrent_cog_requests <= 50:  # User didn't specify high concurrency
            auto_max_concurrent = self.n_consumers * 20  # 20 requests per consumer (I/O bound)
            max_concurrent_cog_requests = max(max_concurrent_cog_requests, auto_max_concurrent)
            logger.debug(f"Auto-tuned max_concurrent_cog_requests to {max_concurrent_cog_requests}")

        # Determine effective consumer count (cap by COG concurrency)
        effective_consumers = max(1, min(self.n_consumers, max_concurrent_cog_requests // self.batch_size))
        logger.debug(f"Effective consumers: {effective_consumers} (from {self.n_consumers} configured)")

        # Prepare cloned endpoints to avoid dangling original handles
        producer_send = send_to_consumers.clone()
        consumer_receives = [
            receive_from_producer.clone() for _ in range(effective_consumers)
        ]
        consumer_sends = [send_to_collector.clone() for _ in range(effective_consumers)]

        # Close originals in the parent so channel closes when clones close
        await send_to_consumers.aclose()
        await send_to_collector.aclose()
        await receive_from_producer.aclose()
        # Keep receive_from_consumers for the collector

        try:
            # Start producer and consumers in background task group
            async def _run_producer_consumers():
                async with create_task_group() as tg:
                    # Start producer
                    tg.start_soon(
                        self.producer.produce,
                        producer_send,
                        stac_items,
                        max_concurrent_cog_requests,
                        max_concurrent_stac_items,
                    )

                    # Start consumers with batch processing for async-tiff
                    for i in range(effective_consumers):
                        consumer = StreamingConsumer(i, self.stats)
                        tg.start_soon(
                            consumer.consume,
                            consumer_receives[i],
                            consumer_sends[i],
                            self.cog_processor,
                            True,  # use_fast_parser
                            self.batch_size,  # Auto-tuned batch size
                        )

            # Start producer-consumer pipeline in background
            import asyncio

            pipeline_task = asyncio.create_task(_run_producer_consumers())

            try:
                # Collect and yield batches from the main thread
                async for batch_stats, unified_records in self.collector.collect(
                    receive_from_consumers, output_batch_size
                ):
                    yield batch_stats, unified_records
            finally:
                # Ensure pipeline task completes
                if not pipeline_task.done():
                    pipeline_task.cancel()
                    try:
                        await pipeline_task
                    except asyncio.CancelledError:
                        pass

        except Exception as exc:
            logger.error(f"Streaming processing error: {exc}")
            raise

        logger.info(
            f"Streaming processing completed: {self.stats.items_processed} items, {self.stats.records_created} records"
        )

        # Clean up COG processor resources to prevent file descriptor leaks (tiff-dumper pattern)
        try:
            if hasattr(self.cog_processor, 'cleanup'):
                await self.cog_processor.cleanup()
                logger.debug("Cleaned up COG processor resources")
        except Exception as e:
            logger.debug(f"Error during COG processor cleanup: {e}")

    async def _cog_producer(self, send_cog_items, stac_items):
        """Producer: Extract individual COG work items from STAC items (like tiff-dumper)."""
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("Starting COG producer")
        async with send_cog_items:
            stac_count = 0
            cog_count = 0

            if hasattr(stac_items, "__aiter__"):
                # Async iterator
                async for stac_item in stac_items:
                    stac_count += 1
                    # Extract COG work items from this STAC item
                    cog_work_items = self._extract_cog_work_items(stac_item)

                    # Send each COG work item to stream
                    for cog_item in cog_work_items:
                        await send_cog_items.send(cog_item)
                        cog_count += 1

                    # Update item stats
                    self.stats.update(items=1)

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug(
                            f"Extracted {len(cog_work_items)} COG items from STAC item {getattr(stac_item, 'id', 'unknown')}"
                        )
            else:
                # Regular iterator
                for stac_item in stac_items:
                    stac_count += 1
                    # Extract COG work items from this STAC item
                    cog_work_items = self._extract_cog_work_items(stac_item)

                    # Send each COG work item to stream
                    for cog_item in cog_work_items:
                        await send_cog_items.send(cog_item)
                        cog_count += 1

                    # Update item stats
                    self.stats.update(items=1)

                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug(
                            f"Extracted {len(cog_work_items)} COG items from STAC item {getattr(stac_item, 'id', 'unknown')}"
                        )

            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(
                    f"COG producer finished: {stac_count} STAC items → {cog_count} COG work items"
                )
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("COG producer closed")

    async def _cog_consumer(self, receive_cog_items, send_records, shared_parser=None):
        """Consumer: Process individual COG work items (unified method)."""
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("Starting COG consumer")
        async with send_records:
            async with receive_cog_items:
                async for cog_item in receive_cog_items:
                    try:
                        # Use shared parser if available, otherwise fall back to individual processing
                        if shared_parser:
                            unified_record = (
                                await self._process_single_cog_item_with_parser(
                                    cog_item, shared_parser
                                )
                            )
                        else:
                            unified_record = await self._process_single_cog_item(
                                cog_item
                            )

                        if unified_record:
                            await send_records.send(unified_record)
                            self.stats.update(items=0, records=1)

                    except Exception as e:
                        logger.error(
                            f"Consumer error processing COG {cog_item.get('asset_key', 'unknown')}: {e}"
                        )
                        self.stats.update(errors=1)
                        continue
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("COG consumer closed")

    async def _output_collector(self, receive_records, batch_size, batch_queue):
        """Output collector: Drain records and form batches concurrently (tiff-dumper pattern)."""
        if logger.isEnabledFor(logging.DEBUG):
            logger.debug("Starting output collector")

        batch_records = []
        batch_count = 0

        try:
            async with receive_records:
                async for unified_record in receive_records:
                    batch_records.append(unified_record)

                    if len(batch_records) >= batch_size:
                        # Send batch to queue (stats creation delegated to collector)
                        await batch_queue.put((None, batch_records.copy()))

                        # Reset for next batch
                        batch_records.clear()
                        batch_count += 1

                # Handle remaining records
                if batch_records:
                    await batch_queue.put((None, batch_records.copy()))
                    batch_count += 1

        except Exception as e:
            logger.error(f"Output collector error: {e}")
        finally:
            # Send sentinel to indicate completion
            await batch_queue.put(None)
            if logger.isEnabledFor(logging.DEBUG):
                logger.debug(f"Output collector finished: {batch_count} batches sent")

    # Removed redundant _monitor_streaming method - use StreamingMonitor instead

    def _extract_cog_work_items(self, stac_item) -> List[Dict[str, Any]]:
        """Extract individual COG work items from a STAC item with existing-key filtering."""
        cog_work_items: List[Dict[str, Any]] = []

        # Determine candidate COG asset keys
        candidate_assets = {
            key: asset
            for key, asset in getattr(stac_item, "assets", {}).items()
            if self.cog_processor.is_cog_asset(asset)
        }

        if not candidate_assets:
            return cog_work_items

        # Filter using existing_key_checker if available
        only_keys = None
        try:
            if self.existing_key_checker is not None:
                scene_id = getattr(stac_item, "id", None)
                collection = getattr(stac_item, "collection_id", None)
                if scene_id:
                    cache_key = (scene_id, collection)
                    if cache_key in self._existing_keys_cache:
                        existing = self._existing_keys_cache[cache_key]
                    else:
                        existing = self.existing_key_checker(scene_id, collection)
                        self._existing_keys_cache[cache_key] = set(existing or set())
                    only_keys = set(candidate_assets.keys()) - set(existing or set())
        except Exception:
            only_keys = None

        # Choose the final keys to process
        keys_to_process = (
            set(candidate_assets.keys()) if only_keys is None else only_keys
        )
        if not keys_to_process:
            return cog_work_items

        # Build minimal work items, include asset title/roles for richer metadata
        for asset_key in keys_to_process:
            asset = candidate_assets[asset_key]
            cog_item = {
                "stac_item_id": getattr(stac_item, "id", None),
                "asset_key": asset_key,
                "href": getattr(asset, "href", None),
                "asset_title": getattr(asset, "title", None),
                "asset_roles": (
                    list(asset.roles) if getattr(asset, "roles", None) else []
                ),
                "stac_metadata": {
                    "collection": getattr(stac_item, "collection_id", None),
                    "datetime": (
                        stac_item.datetime.isoformat()
                        if getattr(stac_item, "datetime", None)
                        else None
                    ),
                    "geometry": getattr(stac_item, "geometry", None),
                    "bbox": getattr(stac_item, "bbox", None),
                    "properties": getattr(stac_item, "properties", {}) or {},
                    "links": [
                        link.to_dict() for link in getattr(stac_item, "links", [])
                    ],
                },
            }
            cog_work_items.append(cog_item)

        return cog_work_items

    async def _process_single_cog_item(
        self, cog_item: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """Process a single COG work item and return unified record (deprecated - use shared parser version)."""
        # This method is deprecated in favor of _process_single_cog_item_with_parser
        # which uses a shared parser for better performance
        logger.warning(
            "Using deprecated _process_single_cog_item - consider using shared parser version"
        )

        try:
            # Use the shared parser approach for consistency
            async with COGHeaderParser(max_concurrent=1) as parser:
                return await self._process_single_cog_item_with_parser(cog_item, parser)
        except Exception as e:
            logger.error(
                f"Error processing COG item {cog_item.get('asset_key', 'unknown')}: {e}"
            )
            return None

    async def _process_single_cog_item_with_parser(
        self, cog_item: Dict[str, Any], shared_parser
    ) -> Optional[Dict[str, Any]]:
        """Process a single COG work item with shared parser (optimized for streaming)."""
        try:
            asset_href = cog_item["href"]
            asset_key = cog_item["asset_key"]

            # Parse COG headers with shared parser (efficient for streaming)
            url = getattr(self.cog_processor, "compute_request_href", lambda x: x)(
                asset_href
            )
            cog_info = await shared_parser.parse_cog_header(url)
            if not cog_info:
                return None
            cog_meta = cog_info.to_dict()

            # Create COG record in expected format
            cog_record = {
                "asset_key": asset_key,
                "asset_href": asset_href,
                "cog_key": asset_key,  # Ensure cog_key is set
                "cog_href": asset_href,
                "asset_title": cog_item.get("asset_title"),
                "asset_roles": cog_item.get("asset_roles", []),
                **cog_meta,
            }

            # Create unified record using existing schema logic
            stac_metadata = cog_item["stac_metadata"]
            stac_item_dict = {
                "id": cog_item["stac_item_id"],
                "collection": stac_metadata["collection"],
                "geometry": stac_metadata["geometry"],
                "bbox": stac_metadata["bbox"],
                "properties": stac_metadata["properties"],
                "links": stac_metadata["links"],
                "assets": {},
            }

            if stac_metadata["datetime"]:
                stac_item_dict["properties"]["datetime"] = stac_metadata["datetime"]

            unified_record = self.unified_schema.create_unified_record(
                stac_item=stac_item_dict, cog_metadata=cog_record
            )

            return unified_record

        except Exception as e:
            logger.error(
                f"Error processing COG item {cog_item.get('asset_key', 'unknown')} with shared parser: {e}"
            )
            return None

# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""S3 configuration and utilities for Delta Lake storage."""

import logging
from typing import Dict, Any, Optional
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)


class S3Config:
    """S3 configuration and client management for Delta Lake operations."""

    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize S3 configuration.

        Args:
            settings: Settings instance (uses global if None)
        """
        if settings is None:
            from data_marketplace.config.settings import settings as global_settings

            settings = global_settings

        self.settings = settings
        self.logger = logger
        self._s3_client = None

    @property
    def s3_client(self):
        """Get or create S3 client."""
        if self._s3_client is None:
            self._s3_client = self._create_s3_client()
        return self._s3_client

    def _create_s3_client(self):
        """Create S3 client with proper configuration."""
        try:
            session = boto3.Session(
                aws_access_key_id=self.settings.s3.access_key_id,
                aws_secret_access_key=self.settings.s3.secret_access_key,
                region_name=self.settings.s3.region,
            )

            client_kwargs = {}
            if self.settings.s3.endpoint_url:
                client_kwargs["endpoint_url"] = self.settings.s3.endpoint_url

            return session.client("s3", **client_kwargs)

        except NoCredentialsError:
            self.logger.error("AWS credentials not found")
            raise
        except Exception as e:
            self.logger.error(f"Failed to create S3 client: {e}")
            raise

    def get_storage_options(self) -> Dict[str, Any]:
        """
        Get storage options for Delta Lake operations.

        Returns:
            Dictionary of storage options
        """
        return self.settings.get_s3_storage_options()

    def bucket_exists(self) -> bool:
        """
        Check if the configured S3 bucket exists.

        Returns:
            True if bucket exists, False otherwise
        """
        try:
            self.s3_client.head_bucket(Bucket=self.settings.s3.bucket_name)
            return True
        except ClientError as e:
            error_code = e.response["Error"]["Code"]
            if error_code == "404":
                return False
            else:
                self.logger.error(f"Error checking bucket: {e}")
                raise

    def create_bucket(self) -> bool:
        """
        Create the configured S3 bucket if it doesn't exist.

        Returns:
            True if bucket was created or already exists
        """
        if self.bucket_exists():
            self.logger.info(f"Bucket {self.settings.s3.bucket_name} already exists")
            return True

        try:
            if self.settings.s3.region == "us-east-1":
                # us-east-1 doesn't need LocationConstraint
                self.s3_client.create_bucket(Bucket=self.settings.s3.bucket_name)
            else:
                self.s3_client.create_bucket(
                    Bucket=self.settings.s3.bucket_name,
                    CreateBucketConfiguration={
                        "LocationConstraint": self.settings.s3.region
                    },
                )

            self.logger.info(f"Created bucket {self.settings.s3.bucket_name}")
            return True

        except ClientError as e:
            self.logger.error(f"Failed to create bucket: {e}")
            return False

    def list_tables(self, prefix: str = "") -> list:
        """
        List Delta tables in the bucket.

        Args:
            prefix: Prefix to filter tables

        Returns:
            List of table paths
        """
        try:
            full_prefix = f"{self.settings.delta.table_root}/{prefix}"
            if not full_prefix.endswith("/"):
                full_prefix += "/"

            response = self.s3_client.list_objects_v2(
                Bucket=self.settings.s3.bucket_name, Prefix=full_prefix, Delimiter="/"
            )

            tables = []
            for prefix_info in response.get("CommonPrefixes", []):
                table_path = prefix_info["Prefix"].rstrip("/")
                table_name = table_path.split("/")[-1]
                tables.append(table_name)

            return tables

        except ClientError as e:
            self.logger.error(f"Failed to list tables: {e}")
            return []

    def table_exists(self, table_name: str) -> bool:
        """
        Check if a Delta table exists.

        Args:
            table_name: Name of the table

        Returns:
            True if table exists
        """
        try:
            table_prefix = f"{self.settings.delta.table_root}/{table_name}/_delta_log/"

            response = self.s3_client.list_objects_v2(
                Bucket=self.settings.s3.bucket_name, Prefix=table_prefix, MaxKeys=1
            )

            return response.get("KeyCount", 0) > 0

        except ClientError as e:
            self.logger.error(f"Error checking table existence: {e}")
            return False

    def delete_table(self, table_name: str) -> bool:
        """
        Delete a Delta table (all objects with the table prefix).

        Args:
            table_name: Name of the table to delete

        Returns:
            True if successful
        """
        try:
            table_prefix = f"{self.settings.delta.table_root}/{table_name}/"

            # List all objects with the table prefix
            paginator = self.s3_client.get_paginator("list_objects_v2")
            pages = paginator.paginate(
                Bucket=self.settings.s3.bucket_name, Prefix=table_prefix
            )

            objects_to_delete = []
            for page in pages:
                for obj in page.get("Contents", []):
                    objects_to_delete.append({"Key": obj["Key"]})

            if not objects_to_delete:
                self.logger.info(f"No objects found for table {table_name}")
                return True

            # Delete objects in batches
            batch_size = 1000
            for i in range(0, len(objects_to_delete), batch_size):
                batch = objects_to_delete[i : i + batch_size]

                self.s3_client.delete_objects(
                    Bucket=self.settings.s3.bucket_name, Delete={"Objects": batch}
                )

            self.logger.info(
                f"Deleted table {table_name} ({len(objects_to_delete)} objects)"
            )
            return True

        except ClientError as e:
            self.logger.error(f"Failed to delete table {table_name}: {e}")
            return False

    def get_table_size(self, table_name: str) -> int:
        """
        Get the total size of a Delta table in bytes.

        Args:
            table_name: Name of the table

        Returns:
            Total size in bytes
        """
        try:
            table_prefix = f"{self.settings.delta.table_root}/{table_name}/"

            paginator = self.s3_client.get_paginator("list_objects_v2")
            pages = paginator.paginate(
                Bucket=self.settings.s3.bucket_name, Prefix=table_prefix
            )

            total_size = 0
            for page in pages:
                for obj in page.get("Contents", []):
                    total_size += obj["Size"]

            return total_size

        except ClientError as e:
            self.logger.error(f"Failed to get table size: {e}")
            return 0

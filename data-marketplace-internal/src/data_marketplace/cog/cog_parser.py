# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
High-performance COG header parser for streaming ingestion.

Optimized for maximum throughput with complete geospatial metadata extraction.
"""

import asyncio
import logging
from typing import List, Dict, Any, Optional
import aiohttp
import struct
from dataclasses import dataclass

logger = logging.getLogger(__name__)


def get_crs_from_tiff_tags(tags: Dict[int, Any]) -> Optional[int]:
    """
    Extract CRS from GeoTIFF tags using multiple methods.
    Copied from full parser for feature completeness.
    """
    # Method 1: GeoTiff WKT string (tag 34737 - GeoAsciiParamsTag)
    if 34737 in tags:
        wkt_val = tags[34737]
        try:
            import re

            # Normalize WKT to string (handle tuple/list/bytes cases)
            if isinstance(wkt_val, (tuple, list)):
                wkt_val = wkt_val[0] if wkt_val else ""
            if isinstance(wkt_val, (bytes, bytearray)):
                try:
                    wkt_val = wkt_val.decode("utf-8", errors="ignore")
                except Exception:
                    wkt_val = wkt_val.decode("ascii", errors="ignore")
            if not isinstance(wkt_val, str):
                wkt_val = str(wkt_val)

            # Look for EPSG code in WKT string
            epsg_match = re.search(r'ID\["EPSG",(\d+)\]', wkt_val)
            if epsg_match:
                return int(epsg_match.group(1))
        except Exception as e:
            logger.debug(f"Failed to parse WKT string: {e}")

    # Method 2: GeoKey directory (tag 34735)
    geokeys = tags.get(34735)
    if geokeys:
        try:
            num_keys = geokeys[3]
            for i in range(4, 4 + (4 * num_keys), 4):
                key_id = geokeys[i]
                tiff_tag_loc = geokeys[i + 1]
                count = geokeys[i + 2]
                value = geokeys[i + 3]

                if key_id in (3072, 2048):  # ProjectedCRS or GeographicCRS
                    if tiff_tag_loc == 0 and count == 1:  # Direct value
                        return int(value)
        except Exception as e:
            logger.debug(f"Failed to parse GeoKey directory: {e}")

    return None


@dataclass
class CogMetadata:
    """COG metadata with complete geospatial support."""

    width: int
    height: int
    tile_width: int
    tile_height: int
    dtype: str
    compression: int
    crs: Optional[int] = None
    transform: Optional[tuple] = None
    predictor: Optional[int] = None
    tile_offsets: Optional[List[int]] = None
    tile_byte_counts: Optional[List[int]] = None
    pixel_scale: Optional[tuple] = None
    tiepoint: Optional[tuple] = None

    def to_dict(self) -> dict:
        """Convert to dictionary format compatible with CogMetadata.to_dict()."""
        # Map compression codes to strings
        compression_map = {
            1: "none",
            5: "lzw",
            7: "jpeg",
            8: "deflate",
            32773: "packbits",
        }
        compression_str = compression_map.get(
            self.compression, f"unknown_{self.compression}"
        )

        # Ensure tile offsets and byte counts are properly typed
        tile_offsets = None
        if self.tile_offsets is not None:
            tile_offsets = [int(offset) for offset in self.tile_offsets]

        tile_byte_counts = None
        if self.tile_byte_counts is not None:
            tile_byte_counts = [int(count) for count in self.tile_byte_counts]

        # Ensure transform is properly typed as list of floats
        transform = None
        if self.transform is not None:
            transform = [float(val) for val in self.transform]

        return {
            "cog_width": int(self.width),
            "cog_height": int(self.height),
            "cog_tile_width": int(self.tile_width),
            "cog_tile_height": int(self.tile_height),
            "cog_dtype": str(self.dtype),
            "cog_crs": f"EPSG:{self.crs}" if self.crs else None,
            "cog_predictor": (
                int(self.predictor) if self.predictor is not None else None
            ),
            "cog_transform": transform,
            "cog_compression": compression_str,
            "cog_tile_offsets": tile_offsets,
            "cog_tile_byte_counts": tile_byte_counts,
            # ❌ INCORRECT: Using pixel_scale for radiometric scaling
            # pixel_scale is spatial resolution (meters/pixel), NOT radiometric scaling
            # This will be fixed in async-tiff implementation
            "cog_scale": (
                float(self.pixel_scale[0])
                if self.pixel_scale and len(self.pixel_scale) > 0
                else None
            ),
            "cog_offset": 0.0,  # Default offset (also incorrect - should be NULL)
        }


class COGHeaderParser:
    """
    High-performance COG header parser for streaming ingestion.

    Optimizations:
    - Minimal HTTP requests (1-2 per COG)
    - Complete geospatial metadata extraction
    - High concurrency with connection pooling
    - Optimized for streaming architecture
    """

    def __init__(self, max_concurrent: int = 1000, timeout: int = 30):
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.session = None
        self.semaphore = asyncio.Semaphore(max_concurrent)

        # Essential TIFF tags for complete geospatial metadata
        self.essential_tags = {
            256: "width",  # ImageWidth
            257: "height",  # ImageLength
            258: "bits_per_sample",  # BitsPerSample
            259: "compression",  # Compression
            317: "predictor",  # Predictor
            322: "tile_width",  # TileWidth
            323: "tile_height",  # TileLength
            324: "tile_offsets",  # TileOffsets
            325: "tile_byte_counts",  # TileByteCounts
            339: "sample_format",  # SampleFormat
            33550: "pixel_scale",  # ModelPixelScaleTag
            33922: "tiepoint",  # ModelTiepointTag
            34735: "geokeys",  # GeoKeyDirectoryTag
            34737: "geo_ascii",  # GeoAsciiParamsTag
        }

        # Data type mapping
        self.dtype_map = {
            (1, 8): "uint8",
            (1, 16): "uint16",
            (1, 32): "uint32",
            (2, 16): "int16",
            (2, 32): "int32",
            (3, 32): "float32",
            (3, 64): "float64",
        }

    async def __aenter__(self):
        """Async context manager entry."""
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=100,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )

        timeout = aiohttp.ClientTimeout(total=self.timeout)
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={"User-Agent": "data-marketplace-fast-parser/1.0"},
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()

    async def parse_cog_header(self, url: str) -> Optional[CogMetadata]:
        """
        Enhanced fast COG header parsing with complete geospatial metadata.

        Extracts all essential metadata including CRS, transform, and tile info.
        """
        async with self.semaphore:
            try:
                # Read larger initial header for geospatial tags (4KB should cover most cases)
                async with self.session.get(
                    url, headers={"Range": "bytes=0-4095"}
                ) as response:
                    if response.status != 206:  # Partial content
                        return None

                    header_bytes = await response.read()

                # Quick TIFF validation and parsing
                if len(header_bytes) < 16:
                    return None

                # Check byte order
                big_endian = header_bytes[0:2] == b"MM"
                endian = ">" if big_endian else "<"

                # Parse version and IFD offset
                version = struct.unpack(f"{endian}H", header_bytes[2:4])[0]
                if version not in (42, 43):  # Standard TIFF versions
                    return None

                if version == 42:
                    ifd_offset = struct.unpack(f"{endian}L", header_bytes[4:8])[0]
                    entry_size = 12
                else:  # BigTIFF
                    ifd_offset = struct.unpack(f"{endian}Q", header_bytes[8:16])[0]
                    entry_size = 20

                # Parse IFD entries with enhanced tag support
                if ifd_offset + 512 > len(header_bytes):
                    # Need more data - read additional bytes for geospatial tags
                    async with self.session.get(
                        url,
                        headers={"Range": f"bytes={ifd_offset}-{ifd_offset + 2048}"},
                    ) as response:
                        if response.status != 206:
                            return None
                        ifd_bytes = await response.read()
                else:
                    ifd_bytes = header_bytes[ifd_offset:]

                # Parse enhanced tags for complete metadata
                if len(ifd_bytes) < 2:
                    return None

                entry_count = struct.unpack(f"{endian}H", ifd_bytes[0:2])[0]
                entry_count = min(
                    entry_count, 100
                )  # Increased limit for geospatial tags

                tags = {}
                for i in range(entry_count):
                    offset = 2 + (i * entry_size)
                    if offset + entry_size > len(ifd_bytes):
                        break

                    entry = ifd_bytes[offset : offset + entry_size]
                    tag = struct.unpack(f"{endian}H", entry[0:2])[0]

                    # Parse all essential tags including geospatial ones
                    if tag in self.essential_tags:
                        type_id = struct.unpack(f"{endian}H", entry[2:4])[0]
                        count = struct.unpack(f"{endian}L", entry[4:8])[0]

                        # Enhanced value extraction for different types
                        if type_id == 3:  # SHORT
                            if count == 1:
                                value = struct.unpack(f"{endian}H", entry[8:10])[0]
                            else:
                                # Multiple values - try to read from offset if in our chunk
                                offset = struct.unpack(f"{endian}L", entry[8:12])[0]
                                if offset + (count * 2) <= len(header_bytes):
                                    data = header_bytes[offset : offset + (count * 2)]
                                    value = struct.unpack(f"{endian}{count}H", data)
                                else:
                                    value = struct.unpack(f"{endian}H", entry[8:10])[
                                        0
                                    ]  # Fallback
                        elif type_id == 4:  # LONG
                            if count == 1:
                                value = struct.unpack(f"{endian}L", entry[8:12])[0]
                            else:
                                # Multiple values - try to read from offset if in our chunk
                                offset = struct.unpack(f"{endian}L", entry[8:12])[0]
                                if offset + (count * 4) <= len(header_bytes):
                                    data = header_bytes[offset : offset + (count * 4)]
                                    value = struct.unpack(f"{endian}{count}L", data)
                                else:
                                    value = struct.unpack(f"{endian}L", entry[8:12])[
                                        0
                                    ]  # Fallback
                        elif type_id == 5:  # RATIONAL - for pixel scale/tiepoint
                            # Try to read RATIONAL values from our chunk (avoid HTTP request)
                            offset = struct.unpack(f"{endian}L", entry[8:12])[0]
                            if offset + (count * 8) <= len(header_bytes):
                                data = header_bytes[offset : offset + (count * 8)]
                                vals = struct.unpack(f"{endian}{count*2}L", data)
                                value = tuple(
                                    vals[i] / vals[i + 1]
                                    for i in range(0, len(vals), 2)
                                )
                            else:
                                value = None  # Skip if not in chunk
                        else:
                            continue

                        tags[tag] = value

                # Extract essential metadata
                width = tags.get(256, 0)
                height = tags.get(257, 0)
                tile_width = tags.get(322, width)
                tile_height = tags.get(323, height)
                compression = tags.get(259, 1)
                predictor = tags.get(317)
                bits_per_sample = tags.get(258, 8)
                sample_format = tags.get(339, 1)

                if width == 0 or height == 0:
                    return None

                # Determine data type
                dtype = self.dtype_map.get((sample_format, bits_per_sample), "uint8")

                # Extract CRS from tags
                crs = get_crs_from_tiff_tags(tags)

                # Calculate transform from pixel_scale and tiepoint if available
                transform = None
                pixel_scale = tags.get(33550)  # ModelPixelScaleTag
                tiepoint = tags.get(33922)  # ModelTiepointTag

                if (
                    pixel_scale
                    and tiepoint
                    and len(pixel_scale) >= 2
                    and len(tiepoint) >= 6
                ):
                    scale_x, scale_y = pixel_scale[0], -pixel_scale[1]
                    translate_x, translate_y = tiepoint[3], tiepoint[4]
                    transform = (scale_x, 0.0, translate_x, 0.0, scale_y, translate_y)

                return CogMetadata(
                    width=int(width),
                    height=int(height),
                    tile_width=int(tile_width),
                    tile_height=int(tile_height),
                    dtype=str(dtype),
                    compression=int(compression),
                    crs=crs,
                    transform=transform,
                    predictor=predictor,
                    tile_offsets=tags.get(324),  # TileOffsets
                    tile_byte_counts=tags.get(325),  # TileByteCounts
                    pixel_scale=pixel_scale,
                    tiepoint=tiepoint,
                )

            except Exception as e:
                logger.debug(f"Fast parse failed for {url}: {e}")
                return None

    async def parse_batch(self, urls: List[str]) -> List[Optional[CogMetadata]]:
        """Parse a batch of COG headers with high concurrency."""
        tasks = [self.parse_cog_header(url) for url in urls]
        return await asyncio.gather(*tasks, return_exceptions=True)


async def parse_cog_headers(
    urls: List[str], max_concurrent: int = 1000
) -> List[Optional[CogMetadata]]:
    """
    Convenience function for COG header parsing.

    Args:
        urls: List of COG URLs to parse
        max_concurrent: Maximum concurrent requests

    Returns:
        List of CogMetadata objects (None for failed parses)
    """
    async with COGHeaderParser(max_concurrent=max_concurrent) as parser:
        return await parser.parse_batch(urls)

# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Configuration settings for the data marketplace system."""

from pathlib import Path
from typing import Optional, Dict, Any
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class S3Settings(BaseSettings):
    """S3 configuration settings."""

    bucket_name: str = Field(
        default="data-marketplace-dev", description="S3 bucket name for data storage"
    )
    region: str = Field(default="us-west-2", description="AWS region")
    access_key_id: Optional[str] = Field(default=None, description="AWS access key ID")
    secret_access_key: Optional[str] = Field(
        default=None, description="AWS secret access key"
    )
    endpoint_url: Optional[str] = Field(
        default=None, description="Custom S3 endpoint URL"
    )

    class Config:
        env_prefix = "S3_"


class DeltaLakeSettings(BaseSettings):
    """Delta Lake configuration settings."""

    table_root: str = Field(
        default="delta-tables", description="Root path for Delta tables"
    )
    master_index_table: str = Field(
        default="master_index", description="Name of master index table"
    )
    vacuum_retention_hours: int = Field(
        default=168,  # 7 days
        description="Retention period for Delta Lake vacuum operations",
    )
    enable_data_skipping: bool = Field(
        default=True, description="Enable Delta Lake data skipping optimization"
    )

    class Config:
        env_prefix = "DELTA_"


class SpatialSettings(BaseSettings):
    """Spatial indexing configuration."""

    s2_cell_level: int = Field(
        default=6, description="S2 cell level for spatial partitioning", ge=0, le=30
    )
    bbox_precision: int = Field(
        default=6, description="Decimal precision for bbox coordinates"
    )

    class Config:
        env_prefix = "SPATIAL_"


class ParquetSettings(BaseSettings):
    """Parquet file configuration optimized for STAC metadata."""

    # Row group size optimized for STAC metadata (~100K rows = ~100MB)
    # Research shows 100K-1M rows optimal for DuckDB performance
    row_group_size: int = Field(
        default=100 * 1024 * 1024,  # 100MB (optimal for ~100K STAC records)
        description="Target row group size in bytes - optimized for metadata workloads",
    )

    # Compression: Snappy optimal for dictionary-heavy data with frequent reads
    compression: str = Field(
        default="snappy",
        description="Compression algorithm - snappy optimal for dictionary data + fast reads",
    )
    compression_level: Optional[int] = Field(
        default=None, description="Compression level (not used for snappy)"
    )

    # Dictionary encoding for string columns (optimal for STAC metadata)
    use_dictionary: bool = Field(
        default=True, description="Enable dictionary encoding for string columns"
    )

    # Bloom filter settings for high-cardinality columns
    enable_bloom_filters: bool = Field(
        default=True, description="Enable Bloom filters for high-cardinality columns"
    )
    bloom_filter_columns: list = Field(
        default=[
            "scene_id",
            "collection",
            "s2_cell_id",
            "visual_href",
            "thumbnail_href",
        ],
        description="Columns to create Bloom filters for - optimized for rasteret queries",
    )

    class Config:
        env_prefix = "PARQUET_"


class APISettings(BaseSettings):
    """API server configuration."""

    host: str = Field(default="0.0.0.0", description="API server host")
    port: int = Field(default=8000, description="API server port")
    workers: int = Field(default=1, description="Number of worker processes")
    max_items_per_request: int = Field(
        default=10000, description="Maximum items returned per API request"
    )
    enable_cors: bool = Field(default=True, description="Enable CORS")

    class Config:
        env_prefix = "API_"


class Settings(BaseSettings):
    """Main configuration settings for the data marketplace."""

    # Environment
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=False, description="Enable debug mode")
    log_level: str = Field(default="INFO", description="Logging level")

    # Component settings
    s3: S3Settings = Field(default_factory=S3Settings)
    delta: DeltaLakeSettings = Field(default_factory=DeltaLakeSettings)
    spatial: SpatialSettings = Field(default_factory=SpatialSettings)
    parquet: ParquetSettings = Field(default_factory=ParquetSettings)
    api: APISettings = Field(default_factory=APISettings)

    # Paths
    workspace_dir: Optional[Path] = Field(
        default=None, description="Local workspace directory for caching"
    )

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False

    @validator("workspace_dir", pre=True)
    def validate_workspace_dir(cls, v):
        if v is None:
            return Path.home() / ".data_marketplace"
        return Path(v)

    def get_s3_storage_options(self) -> Dict[str, Any]:
        """Get S3 storage options for Delta Lake."""
        options = {
            "AWS_REGION": self.s3.region,
        }

        if self.s3.access_key_id:
            options["AWS_ACCESS_KEY_ID"] = self.s3.access_key_id

        if self.s3.secret_access_key:
            options["AWS_SECRET_ACCESS_KEY"] = self.s3.secret_access_key

        if self.s3.endpoint_url:
            options["AWS_ENDPOINT_URL"] = self.s3.endpoint_url
            options["AWS_S3_ALLOW_UNSAFE_RENAME"] = "true"

        return options

    def get_delta_table_path(self, table_name: str) -> str:
        """Get full Delta table path."""
        return f"s3://{self.s3.bucket_name}/{self.delta.table_root}/{table_name}"

    def get_master_index_path(self) -> str:
        """Get master index table path."""
        return self.get_delta_table_path(self.delta.master_index_table)


# Global settings instance
settings = Settings()

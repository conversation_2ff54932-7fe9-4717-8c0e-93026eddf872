# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Advanced Parquet optimization settings for STAC metadata workloads.

This module provides optimized Parquet settings specifically tuned for:
- High dictionary encoding efficiency on repeated values
- Fast reads with Snappy compression
- Optimal row group sizes for metadata-heavy workloads
- Advanced page sizing for better compression and query performance
"""

from typing import Dict, List, Any, Union


class ParquetOptimizationConfig:
    """Configuration for advanced Parquet optimization settings."""

    # Row group size optimized for metadata workloads - aligned with target file size
    ROW_GROUP_SIZE = (
        128 * 1024 * 1024
    )  # 128MB aligned with target file size for better performance

    # Data page size (default 1MB is usually optimal)
    DATA_PAGE_SIZE = 1024 * 1024  # 1MB

    # Content-defined chunking for dynamic page sizing
    CONTENT_DEFINED_CHUNKING = {
        "min_chunk_size": 256 * 1024,  # 256KB
        "max_chunk_size": 1024 * 1024,  # 1MB
        "norm_level": 0,  # Default normalization level
    }

    # Parquet format version (latest stable)
    PARQUET_VERSION = "2.6"

    # Default compression (fast reads as preferred)
    DEFAULT_COMPRESSION = "snappy"

    @classmethod
    def get_stac_table_settings(cls) -> Dict[str, Any]:
        """Get optimized Parquet settings for STAC table."""
        return {
            # Row group and page settings
            "row_group_size": cls.ROW_GROUP_SIZE,
            "data_page_size": cls.DATA_PAGE_SIZE,
            "use_content_defined_chunking": cls.CONTENT_DEFINED_CHUNKING,
            # Dictionary encoding for repeated values
            "use_dictionary": cls.get_stac_dictionary_columns(),
            # Compression settings
            "compression": cls.get_stac_compression_settings(),
            # Parquet format
            "version": cls.PARQUET_VERSION,
            # Bloom filters for high-cardinality columns
            "bloom_filter_columns": cls.get_stac_bloom_filter_columns(),
            # Write batch size for memory efficiency
            "write_batch_size": 10000,
        }

    @classmethod
    def get_cog_headers_table_settings(cls) -> Dict[str, Any]:
        """Get optimized Parquet settings for COG headers table."""
        return {
            # Row group and page settings
            "row_group_size": cls.ROW_GROUP_SIZE,
            "data_page_size": cls.DATA_PAGE_SIZE,
            "use_content_defined_chunking": cls.CONTENT_DEFINED_CHUNKING,
            # Dictionary encoding for repeated values (critical for COG dimensions)
            "use_dictionary": cls.get_cog_dictionary_columns(),
            # Compression settings (ZSTD for large arrays)
            "compression": cls.get_cog_compression_settings(),
            # Parquet format
            "version": cls.PARQUET_VERSION,
            # Bloom filters for high-cardinality columns
            "bloom_filter_columns": cls.get_cog_bloom_filter_columns(),
            # Write batch size for memory efficiency
            "write_batch_size": 10000,
        }

    @classmethod
    def get_stac_dictionary_columns(cls) -> List[str]:
        """Get columns for dictionary encoding in STAC table."""
        return [
            # High-cardinality but repeated values
            "collection",  # Limited number of collections
            "platform",  # Limited number of platforms
            "constellation",  # Limited number of constellations
            "mission",  # Limited number of missions
            "processing_level",  # L1C, L2A, etc. - very few unique values
            "product_type",  # Limited product types
            "stac_version",  # Very few unique versions
            # Instrument and band information
            "instruments",  # Limited instrument types
            "asset_keys",  # Band names - limited unique values
            "cog_keys",  # COG asset keys - limited unique values
        ]

    @classmethod
    def get_cog_dictionary_columns(cls) -> List[str]:
        """Get columns for dictionary encoding in COG headers table - optimized for massive space savings."""
        return [
            # COG dimensions (MASSIVE repetition - critical for space savings)
            # Sentinel-2: 10980x10980 (10m), 5490x5490 (20m), 1830x1830 (60m)
            "cog_width",  # Very few unique values across millions of records
            "cog_height",  # Very few unique values across millions of records
            "cog_tile_width",  # Usually 512 or 1024 - very few unique values
            "cog_tile_height",  # Usually 512 or 1024 - very few unique values
            # Asset identifiers (repeated across bands)
            "cog_key",  # Band names like 'B04', 'red', etc.
            # COG metadata (highly repeated values)
            "cog_dtype",  # Data types like 'uint16', 'float32' - very few unique values
            "cog_compression",  # Compression like 'deflate', 'lzw' - very few unique values
            "cog_crs",  # CRS strings like 'EPSG:4326' - limited unique values
            # Asset metadata
            "cog_title",  # Asset titles - some repetition
            "parser_version",  # Parser version - very few unique values
        ]

    @classmethod
    def get_stac_compression_settings(cls) -> Union[str, Dict[str, str]]:
        """Get compression settings for STAC table."""
        return {
            # Default fast compression for most columns
            "default": cls.DEFAULT_COMPRESSION,
            # Better compression for large text fields
            "stac_properties_json": "zstd",
            "stac_assets_json": "zstd",
        }

    @classmethod
    def get_cog_compression_settings(cls) -> Union[str, Dict[str, str]]:
        """Get compression settings for COG headers table."""
        return {
            # Default fast compression for most columns
            "default": cls.DEFAULT_COMPRESSION,
            # Better compression for large arrays (these can be very large)
            "cog_tile_offsets": "zstd",
            "cog_tile_byte_counts": "zstd",
            "cog_transform": "zstd",
        }

    @classmethod
    def get_stac_bloom_filter_columns(cls) -> List[str]:
        """Get Bloom filter columns for STAC table."""
        return [
            "scene_id",  # Primary key lookups
            "collection",  # Collection filtering
            "s2_cell_id",  # Spatial queries
            "platform",  # Platform filtering
            "constellation",  # Constellation filtering
        ]

    @classmethod
    def get_cog_bloom_filter_columns(cls) -> List[str]:
        """Get Bloom filter columns for COG headers table."""
        return [
            "scene_id",  # Foreign key lookups
            "cog_key",  # Asset filtering
            "cog_href",  # URL lookups
        ]

    @classmethod
    def get_delta_lake_settings(cls) -> Dict[str, Any]:
        """Get Delta Lake specific optimization settings."""
        return {
            # Delta Lake table properties for optimization
            "delta.autoOptimize.optimizeWrite": "true",
            "delta.autoOptimize.autoCompact": "true",
            # Checkpoint interval
            "delta.checkpointInterval": 100,
            # File size targets - larger files for better performance
            "delta.targetFileSize": f"{128 * 1024 * 1024}",  # 128MB target file size
            # Bloom filter settings
            "delta.bloomFilter.enabled": "true",
            "delta.bloomFilter.fpp": "0.1",  # 10% false positive probability
        }


# Convenience functions for easy access
def get_optimized_stac_settings() -> Dict[str, Any]:
    """Get optimized Parquet settings for STAC table."""
    return ParquetOptimizationConfig.get_stac_table_settings()


def get_optimized_cog_settings() -> Dict[str, Any]:
    """Get optimized Parquet settings for COG headers table."""
    return ParquetOptimizationConfig.get_cog_headers_table_settings()


def get_delta_lake_properties() -> Dict[str, Any]:
    """Get Delta Lake table properties for optimization."""
    return ParquetOptimizationConfig.get_delta_lake_settings()


# Export key settings for easy import
__all__ = [
    "ParquetOptimizationConfig",
    "get_optimized_stac_settings",
    "get_optimized_cog_settings",
    "get_delta_lake_properties",
]

#!/usr/bin/env python3
"""
Test the newly created Delta table with machine-readable COG metadata.

This script uses DuckDB to query the Delta table and verify:
1. Machine-readable field names are present
2. Data types are correct
3. Values are properly stored
4. Human-readable conversion works
"""

import asyncio
import logging
from typing import Dict, Any

try:
    import duckdb
    DUCKDB_AVAILABLE = True
except ImportError:
    print("❌ DuckDB not available. Install with: uv add duckdb")
    DUCKDB_AVAILABLE = False

try:
    from data_marketplace.cog.lookup_table_manager import LookupTableManager
    LOOKUP_AVAILABLE = True
except ImportError:
    print("❌ Lookup manager not available")
    LOOKUP_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Table path from our successful ingestion
TABLE_PATH = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_async_tiff_test"


def setup_duckdb_connection():
    """Set up DuckDB connection with S3 and Delta Lake support."""
    if not DUCKDB_AVAILABLE:
        raise ImportError("DuckDB not available")
    
    conn = duckdb.connect()
    
    # Install and load required extensions
    conn.execute("INSTALL delta")
    conn.execute("LOAD delta")
    conn.execute("INSTALL httpfs")
    conn.execute("LOAD httpfs")
    
    # Configure S3 access (using credential chain - IAM role or AWS CLI profile)
    conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain, REGION 'us-west-2')")
    
    logger.info("✅ DuckDB connection configured with Delta Lake and S3 support")
    return conn


def test_table_schema(conn):
    """Test that the table has the correct machine-readable schema."""
    print("\n🔍 Testing Table Schema")
    print("="*40)
    
    try:
        # Get table schema
        result = conn.execute(f"DESCRIBE SELECT * FROM delta_scan('{TABLE_PATH}')").fetchall()
        
        print("📋 Table Schema:")
        schema_dict = {}
        for row in result:
            column_name, data_type = row[0], row[1]
            schema_dict[column_name] = data_type
            print(f"   {column_name:25}: {data_type}")
        
        # Check for machine-readable fields
        expected_fields = [
            "cog_dtype_code",
            "cog_bits_per_sample", 
            "cog_compression_code",
            "cog_crs_code",
            "cog_dn_scale",
            "cog_dn_offset"
        ]
        
        print(f"\n✅ Expected Machine-Readable Fields:")
        all_present = True
        for field in expected_fields:
            if field in schema_dict:
                print(f"   ✅ {field:25}: {schema_dict[field]}")
            else:
                print(f"   ❌ {field:25}: MISSING")
                all_present = False
        
        # Check that deprecated fields are absent
        deprecated_fields = ["cog_dtype", "cog_compression", "cog_crs", "cog_scale", "cog_offset"]
        print(f"\n❌ Deprecated Fields (should be absent):")
        none_present = True
        for field in deprecated_fields:
            if field in schema_dict:
                print(f"   ❌ {field:25}: STILL PRESENT")
                none_present = False
            else:
                print(f"   ✅ {field:25}: correctly absent")
        
        success = all_present and none_present
        print(f"\n🎯 Schema Test: {'✅ PASS' if success else '❌ FAIL'}")
        return success, len(schema_dict)
        
    except Exception as e:
        print(f"❌ Error testing schema: {e}")
        return False, 0


def test_data_content(conn):
    """Test the actual data content and values."""
    print(f"\n🔍 Testing Data Content")
    print("="*35)
    
    try:
        # Get basic table stats
        result = conn.execute(f"SELECT COUNT(*) FROM delta_scan('{TABLE_PATH}')").fetchone()
        row_count = result[0]
        print(f"📊 Total rows: {row_count}")
        
        # Sample some machine-readable values
        query = f"""
        SELECT 
            cog_key,
            cog_dtype_code,
            cog_bits_per_sample,
            cog_compression_code,
            cog_crs_code,
            cog_dn_scale,
            cog_dn_offset,
            cog_width,
            cog_height
        FROM delta_scan('{TABLE_PATH}')
        LIMIT 5
        """
        
        result = conn.execute(query).fetchall()
        
        print(f"\n📋 Sample Data (first 5 rows):")
        print(f"{'COG Key':10} {'DType':5} {'Bits':4} {'Comp':4} {'CRS':6} {'Scale':10} {'Offset':8} {'Width':6} {'Height':6}")
        print("-" * 80)
        
        for row in result:
            cog_key, dtype_code, bits, comp_code, crs_code, dn_scale, dn_offset, width, height = row
            print(f"{str(cog_key)[:10]:10} {dtype_code:5} {bits:4} {comp_code:4} {crs_code:6} {str(dn_scale)[:10]:10} {str(dn_offset)[:8]:8} {width:6} {height:6}")
        
        # Test for NULL handling
        null_query = f"""
        SELECT 
            COUNT(*) as total_rows,
            COUNT(cog_dn_scale) as non_null_dn_scale,
            COUNT(cog_dn_offset) as non_null_dn_offset,
            COUNT(cog_predictor) as non_null_predictor
        FROM delta_scan('{TABLE_PATH}')
        """
        
        result = conn.execute(null_query).fetchone()
        total, dn_scale_count, dn_offset_count, predictor_count = result
        
        print(f"\n📊 NULL Value Analysis:")
        print(f"   Total rows:           {total}")
        print(f"   Non-NULL dn_scale:    {dn_scale_count} ({dn_scale_count/total*100:.1f}%)")
        print(f"   Non-NULL dn_offset:   {dn_offset_count} ({dn_offset_count/total*100:.1f}%)")
        print(f"   Non-NULL predictor:   {predictor_count} ({predictor_count/total*100:.1f}%)")
        
        print(f"\n🎯 Data Content Test: ✅ PASS")
        return True, result
        
    except Exception as e:
        print(f"❌ Error testing data content: {e}")
        return False, None


def test_machine_readable_queries(conn):
    """Test queries using machine-readable values."""
    print(f"\n🔍 Testing Machine-Readable Queries")
    print("="*45)
    
    try:
        # Query by compression code
        query1 = f"""
        SELECT cog_compression_code, COUNT(*) as count
        FROM delta_scan('{TABLE_PATH}')
        GROUP BY cog_compression_code
        ORDER BY count DESC
        """
        
        result = conn.execute(query1).fetchall()
        print(f"📊 Compression Codes:")
        for row in result:
            comp_code, count = row
            print(f"   Code {comp_code}: {count} files")
        
        # Query by data type
        query2 = f"""
        SELECT cog_dtype_code, cog_bits_per_sample, COUNT(*) as count
        FROM delta_scan('{TABLE_PATH}')
        GROUP BY cog_dtype_code, cog_bits_per_sample
        ORDER BY count DESC
        """
        
        result = conn.execute(query2).fetchall()
        print(f"\n📊 Data Types:")
        for row in result:
            dtype_code, bits, count = row
            print(f"   Type {dtype_code}, {bits} bits: {count} files")
        
        # Query by CRS
        query3 = f"""
        SELECT cog_crs_code, COUNT(*) as count
        FROM delta_scan('{TABLE_PATH}')
        WHERE cog_crs_code IS NOT NULL
        GROUP BY cog_crs_code
        ORDER BY count DESC
        """
        
        result = conn.execute(query3).fetchall()
        print(f"\n📊 CRS Codes:")
        for row in result:
            crs_code, count = row
            print(f"   EPSG {crs_code}: {count} files")
        
        print(f"\n🎯 Machine-Readable Queries: ✅ PASS")
        return True
        
    except Exception as e:
        print(f"❌ Error testing queries: {e}")
        return False


async def test_human_readable_conversion(sample_data):
    """Test conversion to human-readable format."""
    print(f"\n🔍 Testing Human-Readable Conversion")
    print("="*45)
    
    if not LOOKUP_AVAILABLE or not sample_data:
        print("❌ Lookup manager or sample data not available")
        return False
    
    try:
        manager = LookupTableManager()
        
        # Convert first sample record
        if not sample_data or len(sample_data) == 0:
            print("❌ No sample data available")
            return False

        sample_row = sample_data[0]
        print(f"🔍 Sample row: {sample_row} (type: {type(sample_row)}, length: {len(sample_row) if hasattr(sample_row, '__len__') else 'N/A'})")

        try:
            cog_key, dtype_code, bits, comp_code, crs_code, dn_scale, dn_offset, width, height = sample_row
        except (ValueError, TypeError) as e:
            print(f"❌ Error unpacking sample row: {e}")
            print(f"   Sample row: {sample_row}")
            return False
        
        # Create record dictionary
        cog_record = {
            "cog_key": cog_key,
            "cog_dtype_code": dtype_code,
            "cog_bits_per_sample": bits,
            "cog_compression_code": comp_code,
            "cog_crs_code": crs_code,
            "cog_dn_scale": dn_scale,
            "cog_dn_offset": dn_offset,
            "cog_width": width,
            "cog_height": height,
        }
        
        # Convert to human-readable
        human_readable = await manager.convert_record(cog_record)
        
        print(f"📋 Sample Record Conversion:")
        print(f"   COG Key: {cog_key}")
        print(f"   Machine-Readable → Human-Readable:")
        for field, value in human_readable.items():
            print(f"      {field:20}: {value}")
        
        print(f"\n🎯 Human-Readable Conversion: ✅ PASS")
        return True
        
    except Exception as e:
        print(f"❌ Error testing conversion: {e}")
        return False


async def main():
    """Run comprehensive Delta table tests."""
    print("🧪 Delta Table Machine-Readable Testing")
    print("="*60)
    print(f"Table: {TABLE_PATH}")
    
    if not DUCKDB_AVAILABLE:
        print("❌ DuckDB not available")
        return 1
    
    try:
        # Setup DuckDB connection
        conn = setup_duckdb_connection()
        
        # Test 1: Schema validation
        schema_success, field_count = test_table_schema(conn)
        
        # Test 2: Data content
        data_success, sample_data = test_data_content(conn)
        
        # Test 3: Machine-readable queries
        query_success = test_machine_readable_queries(conn)
        
        # Test 4: Human-readable conversion
        conversion_success = await test_human_readable_conversion(sample_data)
        
        # Summary
        all_tests = [schema_success, data_success, query_success, conversion_success]
        overall_success = all(all_tests)
        
        print(f"\n🎯 OVERALL RESULTS:")
        print(f"   Schema Validation:      {'✅ PASS' if schema_success else '❌ FAIL'}")
        print(f"   Data Content:           {'✅ PASS' if data_success else '❌ FAIL'}")
        print(f"   Machine-Readable Queries: {'✅ PASS' if query_success else '❌ FAIL'}")
        print(f"   Human-Readable Conversion: {'✅ PASS' if conversion_success else '❌ FAIL'}")
        print(f"   Overall:                {'✅ ALL PASS' if overall_success else '❌ SOME FAILED'}")
        
        if overall_success:
            print(f"\n🎉 Delta table successfully created with machine-readable schema!")
            print(f"   ✅ {field_count} fields in schema")
            print(f"   ✅ Machine-readable values stored efficiently")
            print(f"   ✅ Human-readable conversion working")
            print(f"   ✅ Ready for production analytics!")
        else:
            print(f"\n⚠️  Some tests failed - check implementation")
        
        conn.close()
        return 0 if overall_success else 1
        
    except Exception as e:
        print(f"❌ Error in testing: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))

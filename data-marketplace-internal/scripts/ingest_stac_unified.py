# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Production STAC ingestion script for unified table approach.

This script ingests STAC collections into a unified Delta Lake table where each
COG asset gets its own row with repeated scene metadata for optimal cross-collection queries.
"""

import asyncio
import argparse
import logging
import sys
import os
import subprocess
from pathlib import Path

from data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester

# Removed performance_config - using hardcoded optimal values

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def get_bucket_region(bucket_name: str) -> str:
    """
    Get the actual region of an S3 bucket using AWS CLI.

    Args:
        bucket_name: S3 bucket name

    Returns:
        AWS region string
    """
    try:
        result = subprocess.run(
            ["aws", "s3api", "get-bucket-location", "--bucket", bucket_name],
            capture_output=True,
            text=True,
            timeout=10,
        )
        if result.returncode == 0:
            import json

            location = json.loads(result.stdout).get("LocationConstraint")
            # AWS returns None for us-east-1, empty string for some regions
            if location is None or location == "":
                return "us-east-1"
            return location
        else:
            logger.warning(f"Could not get bucket region: {result.stderr}")
            return "us-west-2"  # fallback
    except Exception as e:
        logger.warning(f"Error getting bucket region: {e}")
        return "us-west-2"  # fallback


def get_storage_options(output_path: str) -> dict:
    """
    Get storage options for Delta Lake based on the output path.

    Args:
        output_path: Output path (local, s3://, etc.)

    Returns:
        Dictionary of storage options for Delta Lake
    """
    storage_options = {}

    # Check if it's an S3 path
    if output_path.startswith("s3://"):
        # Extract bucket name from S3 path
        bucket_name = output_path.replace("s3://", "").split("/")[0]

        # Get the actual bucket region
        aws_region = get_bucket_region(bucket_name)
        logger.info(f"Detected S3 bucket '{bucket_name}' in region: {aws_region}")

        # Try to get AWS credentials from environment
        aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
        aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
        aws_session_token = os.getenv("AWS_SESSION_TOKEN")

        # Always set the region for S3 operations (Delta Lake expects AWS_REGION)
        storage_options["AWS_REGION"] = aws_region

        if aws_access_key and aws_secret_key:
            # Use explicit credentials (Delta Lake expects AWS_* prefixed keys)
            storage_options["AWS_ACCESS_KEY_ID"] = aws_access_key
            storage_options["AWS_SECRET_ACCESS_KEY"] = aws_secret_key
            if aws_session_token:
                storage_options["AWS_SESSION_TOKEN"] = aws_session_token
            logger.info("Using AWS credentials from environment variables")
        else:
            # Use IAM role or AWS CLI profile - no explicit credentials needed
            logger.info("Using IAM role or AWS CLI profile")

    return storage_options


async def main():
    """Main ingestion function."""
    parser = argparse.ArgumentParser(
        description="Ingest STAC collections into unified Delta Lake table with auto-tuned performance"
    )
    parser.add_argument(
        "stac_api_url",
        help="STAC API endpoint URL (e.g., https://earth-search.aws.element84.com/v1)",
    )
    parser.add_argument(
        "collection_id", help="STAC collection ID (e.g., sentinel-2-l2a)"
    )
    parser.add_argument(
        "--output-path",
        default="./data/unified_stac_table",
        help="Output path for unified Delta Lake table (default: ./data/unified_stac_table)",
    )
    parser.add_argument(
        "--max-items",
        type=int,
        help="Maximum number of STAC items to process (for testing)",
    )
    parser.add_argument(
        "--datetime-range", help="Date range filter (e.g., '2024-01-01/2024-01-31')"
    )
    parser.add_argument(
        "--bbox",
        help="Bounding box filter as comma-separated values: west,south,east,north",
    )
    parser.add_argument(
        "--batch-size",
        type=int,
        default=2000,
        help="Number of STAC items to process per batch (default: 2000)",
    )
    # Note: Concurrency parameters (max-concurrent-cog, max-concurrent-stac) are now
    # auto-tuned based on CPU cores for optimal performance. No manual tuning needed.
    parser.add_argument(
        "--force-reingest",
        action="store_true",
        help="Force reingestion using MERGE mode instead of APPEND (allows overwriting existing data)",
    )
    parser.add_argument(
        "--vacuum-retention-hours",
        type=int,
        default=168,  # 7 days default (minimum safe retention)
        help="Hours to retain old files before vacuum cleanup (default: 168 hours = 7 days, minimum safe retention)",
    )
    parser.add_argument(
        "--skip-vacuum",
        action="store_true",
        help="Skip vacuum cleanup of old/orphan files",
    )
    parser.add_argument(
        "--skip-optimize",
        action="store_true",
        help="Skip table optimization/compaction",
    )
    parser.add_argument(
        "--io-optimized",
        action="store_true",
        help="Use aggressive I/O-optimized concurrency (inspired by tiff-dumper: 1000 consumers)",
    )
    parser.add_argument(
        "--log-level",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        default="INFO",
        help="Logging level (default: INFO)",
    )

    # Removed performance scenario - using hardcoded optimal settings

    args = parser.parse_args()

    # Set logging level
    logging.getLogger().setLevel(getattr(logging, args.log_level))

    # Parse bbox if provided
    bbox = None
    if args.bbox:
        try:
            bbox = [float(x.strip()) for x in args.bbox.split(",")]
            if len(bbox) != 4:
                raise ValueError("Bbox must have exactly 4 values")
        except ValueError as e:
            logger.error(
                f"Invalid bbox format: {args.bbox}. Expected: west,south,east,north. Error: {e}"
            )
            sys.exit(1)

    # Create output directory if it doesn't exist (only for local paths)
    if not args.output_path.startswith("s3://"):
        output_path = Path(args.output_path)
        output_path.parent.mkdir(parents=True, exist_ok=True)

    # Get storage options for S3 or other cloud storage
    storage_options = get_storage_options(args.output_path)

    # Initialize ingester with storage options and hardcoded optimal settings
    ingester = DeltaStacIngester(
        unified_table_path=args.output_path,
        storage_options=storage_options,
        stac_api_url=args.stac_api_url,  # For federation metadata
    )

    logger.info("🚀 Starting Unified STAC Ingestion")
    logger.info("=" * 60)
    logger.info(f"STAC API: {args.stac_api_url}")
    logger.info(f"Collection: {args.collection_id}")
    logger.info(f"Output: {args.output_path}")
    logger.info(f"Max items: {args.max_items or 'unlimited'}")
    logger.info(f"Date range: {args.datetime_range or 'all'}")
    logger.info(f"Bbox: {bbox or 'global'}")
    logger.info(f"Batch size: {args.batch_size}")
    logger.info(f"Concurrency: Auto-tuned based on CPU cores")
    logger.info(f"Force reingest: {'Yes (MERGE mode)' if args.force_reingest else 'No (APPEND mode)'}")
    logger.info(f"Cleanup: Vacuum {'disabled' if args.skip_vacuum else f'{args.vacuum_retention_hours}h retention'}, Optimize {'disabled' if args.skip_optimize else 'enabled'}")
    logger.info(f"Concurrency mode: {'I/O-optimized (aggressive)' if args.io_optimized else 'Auto-tuned (conservative)'}")

    # Log storage configuration for debugging
    if storage_options:
        logger.info(f"Storage options configured: {list(storage_options.keys())}")
    else:
        logger.info("No storage options configured (local filesystem)")

    try:
        # Run ingestion (concurrency auto-tuned based on CPU cores)
        results = await ingester.ingest_stac_collection(
            stac_api_url=args.stac_api_url,
            collection_id=args.collection_id,
            max_items=args.max_items,
            datetime_range=args.datetime_range,
            bbox=bbox,
            batch_size=args.batch_size,
            force_reingest=args.force_reingest,
            vacuum_retention_hours=args.vacuum_retention_hours,
            skip_vacuum=args.skip_vacuum,
            skip_optimize=args.skip_optimize,
            io_optimized=args.io_optimized,
            # max_concurrent_cog_requests and max_concurrent_stac_items now auto-tuned
        )

        # Print results
        logger.info("✅ INGESTION COMPLETED SUCCESSFULLY")
        logger.info("=" * 60)
        logger.info(f"STAC items processed: {results['stac_items_processed']:,}")
        logger.info(f"COG assets processed: {results.get('cog_assets_processed', 'N/A'):,}" if isinstance(results.get('cog_assets_processed'), int) else f"COG assets processed: {results.get('cog_assets_processed', 'N/A')}")
        logger.info(f"Unified records written: {results['unified_records_written']:,}")

        # Show total table rows if available
        total_table_rows = results.get('performance', {}).get('total_table_rows')
        if total_table_rows:
            logger.info(f"Total table rows: {total_table_rows:,}")

        logger.info(f"Duration: {results['duration_seconds']:.2f} seconds")
        logger.info(
            f"Performance: {results['performance']['items_per_second']:.2f} items/sec"
        )
        logger.info(
            f"Total records per second: {results['performance']['unified_records_per_second']:.2f}"
        )

        if results["errors"]:
            logger.warning(f"Errors encountered: {len(results['errors'])}")
            for error in results["errors"][:5]:  # Show first 5 errors
                logger.warning(f"  - {error}")
            if len(results["errors"]) > 5:
                logger.warning(f"  ... and {len(results['errors']) - 5} more errors")

        logger.info(f"🎉 Unified table created at: {args.output_path}")
        logger.info("Ready for cross-collection spatial and temporal queries!")

    except KeyboardInterrupt:
        logger.info("Ingestion interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Ingestion failed: {e}", exc_info=True)
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())

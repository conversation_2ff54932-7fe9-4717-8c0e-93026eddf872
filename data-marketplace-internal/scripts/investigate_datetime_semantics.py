#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
DateTime Semantics Investigation Script

CRITICAL: This script investigates datetime fields in the unified_stac_table to understand:
1. What each datetime field represents (satellite capture vs administrative times)
2. Data quality and consistency of datetime fields
3. Timezone handling and format consistency
4. Relationship between datetime fields and current partitioning
5. Whether we're using the correct datetime (satellite capture time) for partitioning

This analysis is essential before any table restructuring to ensure we don't lose
temporal accuracy or introduce datetime errors.

Usage:
    # Full investigation
    uv run scripts/investigate_datetime_semantics.py

    # Quick sample analysis
    uv run scripts/investigate_datetime_semantics.py --quick --sample-size 1000

    # Focus on specific collections
    uv run scripts/investigate_datetime_semantics.py --collection sentinel-2-l2a
"""

import argparse
import logging
import sys
import time
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Table configuration
TABLE_PATH = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table"


def setup_storage_options() -> dict:
    """Setup storage options for S3 access."""
    return {
        "AWS_REGION": "us-west-2",
        "AWS_DEFAULT_REGION": "us-west-2"
    }


def investigate_schema_and_types(table_path: str, storage_options: dict) -> Dict[str, Any]:
    """Investigate table schema focusing on datetime-related fields."""
    try:
        import duckdb
        from deltalake import DeltaTable
        
        logger.info("🔍 Investigating table schema and datetime fields...")
        
        # Get Delta table metadata
        dt = DeltaTable(table_path, storage_options=storage_options)
        schema = dt.schema()
        
        # Setup DuckDB
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Get detailed schema info
        try:
            schema_result = conn.execute(f"DESCRIBE delta_scan('{table_path}')").fetchall()
            schema_info = {row[0]: row[1] for row in schema_result}
        except Exception as e:
            logger.warning(f"Could not get schema via DuckDB: {e}")
            # Fallback to Delta Lake schema
            schema_info = {field.name: str(field.type) for field in schema.fields}
        
        # Identify datetime-related columns
        datetime_columns = {}
        for col_name, col_type in schema_info.items():
            if any(keyword in col_name.lower() for keyword in ['date', 'time', 'created', 'updated']):
                datetime_columns[col_name] = col_type
        
        # Get partition information
        partitions = dt.metadata().partition_columns if hasattr(dt.metadata(), 'partition_columns') else []
        
        logger.info(f"📊 Schema Analysis:")
        logger.info(f"   Total columns: {len(schema_info)}")
        logger.info(f"   DateTime-related columns: {len(datetime_columns)}")
        logger.info(f"   Current partitions: {partitions}")
        
        logger.info(f"📅 DateTime-related columns found:")
        for col, dtype in datetime_columns.items():
            logger.info(f"   {col}: {dtype}")
        
        conn.close()
        
        return {
            'schema_info': schema_info,
            'datetime_columns': datetime_columns,
            'partitions': partitions,
            'total_columns': len(schema_info)
        }
        
    except Exception as e:
        logger.error(f"Error investigating schema: {e}")
        raise


def sample_datetime_data(table_path: str, storage_options: dict, sample_size: int = 100) -> Dict[str, Any]:
    """Sample actual datetime data to understand values and formats."""
    try:
        import duckdb
        
        logger.info(f"📊 Sampling {sample_size} records to analyze datetime values...")
        
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Sample data focusing on datetime fields - simplified query
        sample_query = f"""
            SELECT
                scene_id,
                collection,
                platform,
                "datetime",
                created,
                updated,
                day,
                year,
                month
            FROM delta_scan('{table_path}')
            USING SAMPLE {sample_size}
            ORDER BY "datetime" DESC
        """
        
        sample_data = conn.execute(sample_query).fetchall()
        
        if not sample_data:
            logger.error("No sample data retrieved")
            return {}
        
        # Analyze the sample
        logger.info(f"📈 Sample Data Analysis ({len(sample_data)} records):")

        # Check datetime ranges
        datetimes = [row[3] for row in sample_data if row[3] is not None]
        if datetimes:
            min_dt = min(datetimes)
            max_dt = max(datetimes)
            logger.info(f"   DateTime range: {min_dt} to {max_dt}")

        # Check for null values
        null_datetime = sum(1 for row in sample_data if row[3] is None)
        null_created = sum(1 for row in sample_data if row[4] is None)
        null_updated = sum(1 for row in sample_data if row[5] is None)

        logger.info(f"   Null values - datetime: {null_datetime}, created: {null_created}, updated: {null_updated}")

        # Manual partition alignment check
        year_mismatches = 0
        month_mismatches = 0

        for row in sample_data:
            if row[3] is not None:  # datetime not null
                dt = row[3]
                if hasattr(dt, 'year'):
                    if row[7] != dt.year:  # year partition mismatch
                        year_mismatches += 1
                    if row[8] != dt.month:  # month partition mismatch
                        month_mismatches += 1

        logger.info(f"   Year partition alignment: {len(sample_data) - year_mismatches}/{len(sample_data)} correct")
        logger.info(f"   Month partition alignment: {len(sample_data) - month_mismatches}/{len(sample_data)} correct")

        if year_mismatches > 0 or month_mismatches > 0:
            logger.warning(f"⚠️  Found partition misalignment! This suggests datetime issues.")

        # Show sample records
        logger.info(f"📋 Sample records (first 5):")
        for i, row in enumerate(sample_data[:5]):
            logger.info(f"   {i+1}. scene_id: {row[0]}")
            logger.info(f"      collection: {row[1]}, platform: {row[2]}")
            logger.info(f"      datetime: {row[3]}")
            logger.info(f"      created: {row[4]}, updated: {row[5]}")
            logger.info(f"      partitions - year: {row[7]}, month: {row[8]}, day: {row[6]}")
            logger.info("")
        
        conn.close()
        
        return {
            'sample_data': sample_data,
            'datetime_range': (min_dt, max_dt) if datetimes else None,
            'year_mismatches': year_mismatches,
            'month_mismatches': month_mismatches,
            'null_counts': {
                'datetime': null_datetime,
                'created': null_created,
                'updated': null_updated
            }
        }
        
    except Exception as e:
        logger.error(f"Error sampling datetime data: {e}")
        raise


def analyze_datetime_consistency(table_path: str, storage_options: dict) -> Dict[str, Any]:
    """Analyze datetime consistency across collections and platforms."""
    try:
        import duckdb
        
        logger.info("🔍 Analyzing datetime consistency across collections...")
        
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Analyze by collection
        collection_analysis = conn.execute(f"""
            SELECT 
                collection,
                platform,
                COUNT(*) as record_count,
                COUNT("datetime") as datetime_count,
                COUNT(created) as created_count,
                COUNT(updated) as updated_count,
                MIN("datetime") as min_datetime,
                MAX("datetime") as max_datetime,
                -- Check for timezone patterns (look for consistent hour patterns)
                MODE(EXTRACT(hour FROM "datetime")) as common_hour,
                COUNT(DISTINCT DATE("datetime")) as unique_dates,
                -- Check partition alignment
                SUM(CASE WHEN year = EXTRACT(year FROM "datetime") THEN 1 ELSE 0 END) as year_aligned,
                SUM(CASE WHEN month = EXTRACT(month FROM "datetime") THEN 1 ELSE 0 END) as month_aligned
            FROM delta_scan('{table_path}')
            GROUP BY collection, platform
            ORDER BY record_count DESC
            LIMIT 20
        """).fetchall()
        
        logger.info(f"📊 Collection Analysis (top 20 by record count):")
        for row in collection_analysis:
            collection, platform, total, dt_count, cr_count, up_count = row[:6]
            min_dt, max_dt, common_hour, unique_dates, year_aligned, month_aligned = row[6:]
            
            logger.info(f"   {collection} ({platform}):")
            logger.info(f"     Records: {total:,}, DateTime coverage: {dt_count/total*100:.1f}%")
            logger.info(f"     Date range: {min_dt} to {max_dt}")
            logger.info(f"     Common hour: {common_hour}, Unique dates: {unique_dates:,}")
            logger.info(f"     Partition alignment: {year_aligned/total*100:.1f}% year, {month_aligned/total*100:.1f}% month")
            
            if year_aligned < total or month_aligned < total:
                logger.warning(f"     ⚠️  Partition misalignment detected!")
            logger.info("")
        
        # Check for datetime vs created/updated differences
        time_diff_analysis = conn.execute(f"""
            SELECT 
                AVG(EXTRACT(epoch FROM (created - "datetime"))/3600) as avg_created_delay_hours,
                AVG(EXTRACT(epoch FROM (updated - "datetime"))/3600) as avg_updated_delay_hours,
                MIN(EXTRACT(epoch FROM (created - "datetime"))/3600) as min_created_delay_hours,
                MAX(EXTRACT(epoch FROM (created - "datetime"))/3600) as max_created_delay_hours,
                COUNT(CASE WHEN created < "datetime" THEN 1 END) as created_before_datetime,
                COUNT(CASE WHEN updated < "datetime" THEN 1 END) as updated_before_datetime
            FROM delta_scan('{table_path}')
            WHERE "datetime" IS NOT NULL AND created IS NOT NULL AND updated IS NOT NULL
            LIMIT 1
        """).fetchone()
        
        if time_diff_analysis:
            avg_created_delay, avg_updated_delay, min_created_delay, max_created_delay = time_diff_analysis[:4]
            created_before, updated_before = time_diff_analysis[4:]
            
            logger.info(f"⏰ Time Difference Analysis:")
            logger.info(f"   Average created delay: {avg_created_delay:.2f} hours after datetime")
            logger.info(f"   Average updated delay: {avg_updated_delay:.2f} hours after datetime")
            logger.info(f"   Created delay range: {min_created_delay:.2f} to {max_created_delay:.2f} hours")
            logger.info(f"   Records with created < datetime: {created_before:,}")
            logger.info(f"   Records with updated < datetime: {updated_before:,}")
            
            if created_before > 0 or updated_before > 0:
                logger.warning(f"⚠️  Found records where created/updated is before datetime - this suggests datetime might not be capture time!")
        
        conn.close()
        
        return {
            'collection_analysis': collection_analysis,
            'time_diff_analysis': time_diff_analysis
        }
        
    except Exception as e:
        logger.error(f"Error analyzing datetime consistency: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(
        description="Investigate datetime semantics in unified_stac_table",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("--quick", action="store_true",
                       help="Quick analysis with smaller sample size")
    parser.add_argument("--sample-size", type=int, default=1000,
                       help="Sample size for data analysis (default: 1000)")
    parser.add_argument("--collection", type=str,
                       help="Focus analysis on specific collection")
    
    args = parser.parse_args()
    
    if args.quick:
        args.sample_size = min(args.sample_size, 100)
    
    logger.info("🔍 DateTime Semantics Investigation")
    logger.info("=" * 60)
    logger.info("⚠️  CRITICAL: Understanding datetime fields before restructuring")
    logger.info(f"📍 Table: {TABLE_PATH}")
    logger.info(f"📊 Sample size: {args.sample_size}")
    
    start_time = time.time()
    
    try:
        storage_options = setup_storage_options()
        
        # Step 1: Investigate schema
        schema_info = investigate_schema_and_types(TABLE_PATH, storage_options)
        
        # Step 2: Sample datetime data
        sample_info = sample_datetime_data(TABLE_PATH, storage_options, args.sample_size)
        
        # Step 3: Analyze consistency (unless quick mode)
        if not args.quick:
            consistency_info = analyze_datetime_consistency(TABLE_PATH, storage_options)
        
        # Summary and recommendations
        duration = time.time() - start_time
        logger.info("")
        logger.info("📋 INVESTIGATION SUMMARY")
        logger.info("=" * 40)
        logger.info(f"⏱️  Analysis duration: {duration:.2f}s")
        
        # Critical findings
        if sample_info.get('year_mismatches', 0) > 0 or sample_info.get('month_mismatches', 0) > 0:
            logger.error("🚨 CRITICAL: Partition misalignment detected!")
            logger.error("   Current year/month partitions don't match datetime field")
            logger.error("   This suggests datetime field issues or incorrect partitioning")
        
        null_datetime = sample_info.get('null_counts', {}).get('datetime', 0)
        if null_datetime > 0:
            logger.warning(f"⚠️  Found {null_datetime} null datetime values in sample")
        
        # Recommendations
        logger.info("")
        logger.info("🎯 RECOMMENDATIONS:")
        
        if sample_info.get('year_mismatches', 0) == 0 and sample_info.get('month_mismatches', 0) == 0:
            logger.info("✅ Partition alignment looks good - datetime field appears correct")
            logger.info("✅ Safe to proceed with date-level partitioning using 'datetime' field")
        else:
            logger.error("❌ DO NOT PROCEED with restructuring until datetime issues are resolved")
            logger.error("❌ Investigate partition misalignment before any changes")
        
        if null_datetime > 0:
            logger.warning("⚠️  Address null datetime values before restructuring")
        
        logger.info("")
        logger.info("📋 Next steps:")
        logger.info("1. Review the analysis results above")
        logger.info("2. Validate that 'datetime' field represents satellite capture time")
        logger.info("3. Address any datetime issues found")
        logger.info("4. Only then proceed with table restructuring")
        
        return 0
        
    except Exception as e:
        logger.error(f"❌ Investigation failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

#!/bin/bash
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

# Delta Lake Table Cleanup Examples
# 
# This script shows common usage patterns for the clean_delta_table.py script

set -e

# Configuration
TABLE_PATH="s3://tf-datalake-bucket/deltalake-tables/unified_stac_table"
LOCAL_TABLE="./data/test_table"

echo "🧹 Delta Lake Table Cleanup Examples"
echo "===================================="

echo ""
echo "1. 📊 Check table statistics only (no changes)"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH --stats-only"

echo ""
echo "2. 🔍 Dry run - see what would be cleaned (safe)"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH --dry-run"

echo ""
echo "3. 🧹 Basic vacuum with default 7-day retention"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH"

echo ""
echo "4. ⚡ Aggressive cleanup - 24 hour retention"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH --vacuum-hours 24"

echo ""
echo "5. 🔧 Optimize small files (compact to 128MB)"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH --optimize"

echo ""
echo "6. 📊 Z-order optimization for spatial queries"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH --optimize --zorder-columns \"year,month,collection\""

echo ""
echo "7. 💪 Full maintenance with custom settings"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH \\"
echo "    --vacuum-hours 72 \\"
echo "    --optimize \\"
echo "    --target-file-size 268435456 \\"
echo "    --zorder-columns \"year,month\""

echo ""
echo "8. 🏠 Local table cleanup"
echo "uv run scripts/clean_delta_table.py $LOCAL_TABLE --optimize"

echo ""
echo "9. 🚨 Emergency cleanup - very aggressive (1 hour retention)"
echo "echo 'WARNING: This will delete files modified in the last hour!'"
echo "uv run scripts/clean_delta_table.py $TABLE_PATH --vacuum-hours 1 --optimize"

echo ""
echo "📋 Common Retention Periods:"
echo "   1 hour   = --vacuum-hours 1    (emergency cleanup)"
echo "   1 day    = --vacuum-hours 24   (aggressive)"
echo "   3 days   = --vacuum-hours 72   (moderate)"
echo "   7 days   = --vacuum-hours 168  (default, safe)"
echo "   30 days  = --vacuum-hours 720  (conservative)"

echo ""
echo "📋 Common File Sizes:"
echo "   64MB  = --target-file-size 67108864"
echo "   128MB = --target-file-size 134217728  (default)"
echo "   256MB = --target-file-size 268435456"
echo "   512MB = --target-file-size 536870912"

echo ""
echo "📋 Common Z-order Patterns:"
echo "   Temporal: --zorder-columns \"year,month,day\""
echo "   Spatial:  --zorder-columns \"year,month,collection\""
echo "   Mixed:    --zorder-columns \"collection,year,month\""

echo ""
echo "🎯 Recommended Workflow:"
echo "1. Check stats:     uv run scripts/clean_delta_table.py TABLE --stats-only"
echo "2. Dry run:         uv run scripts/clean_delta_table.py TABLE --dry-run"
echo "3. Safe cleanup:    uv run scripts/clean_delta_table.py TABLE --optimize"
echo "4. Check results:   uv run scripts/clean_delta_table.py TABLE --stats-only"

#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Memory-Efficient DateTime Semantics Investigation

CRITICAL: Investigates datetime fields following established memory-efficient patterns:
- DuckDB memory limit: 5.5GB, 2 threads
- No full table loads - only COUNT() and small samples
- Chunked processing following proven 5,000 row patterns
- Validates satellite capture time vs administrative timestamps

This analysis is essential before any table restructuring to ensure we don't lose
temporal accuracy or introduce datetime errors.

Usage:
    # Memory-efficient investigation (recommended)
    uv run scripts/investigate_datetime_memory_efficient.py

    # Quick sample only
    uv run scripts/investigate_datetime_memory_efficient.py --quick
"""

import argparse
import logging
import sys
import time
from typing import Dict, Any

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Table configuration
TABLE_PATH = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table"


def setup_duckdb_memory_efficient():
    """Setup DuckDB with memory-efficient settings following established patterns."""
    import duckdb
    
    conn = duckdb.connect()
    conn.execute("INSTALL delta")
    conn.execute("LOAD delta")
    
    # Configure DuckDB for 7GB RAM environment (following clean_delta_table.py patterns)
    conn.execute("SET memory_limit='5.5GB'")  # Leave 1.5GB for OS/other processes
    conn.execute("SET threads=2")  # Reduce threads to save memory
    conn.execute("SET preserve_insertion_order=false")  # Save memory on sorting
    
    # Configure S3 access
    conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
    
    return conn


def get_basic_table_info(table_path: str) -> Dict[str, Any]:
    """Get basic table info without loading data - memory efficient."""
    try:
        from deltalake import DeltaTable
        
        logger.info("📊 Getting basic table information...")
        
        # Get Delta table metadata (no data loading)
        storage_options = {"AWS_REGION": "us-west-2", "AWS_DEFAULT_REGION": "us-west-2"}
        dt = DeltaTable(table_path, storage_options=storage_options)
        
        schema = dt.schema()
        partitions = dt.metadata().partition_columns if hasattr(dt.metadata(), 'partition_columns') else []
        
        # Identify datetime columns from schema
        datetime_columns = {}
        for field in schema.fields:
            if any(keyword in field.name.lower() for keyword in ['date', 'time', 'created', 'updated']):
                datetime_columns[field.name] = str(field.type)
        
        logger.info(f"📈 Basic Info:")
        logger.info(f"   Total columns: {len(schema.fields)}")
        logger.info(f"   DateTime columns: {len(datetime_columns)}")
        logger.info(f"   Current partitions: {partitions}")
        logger.info(f"   Table version: {dt.version()}")
        
        logger.info(f"📅 DateTime columns found:")
        for col, dtype in datetime_columns.items():
            logger.info(f"   {col}: {dtype}")
        
        return {
            'total_columns': len(schema.fields),
            'datetime_columns': datetime_columns,
            'partitions': partitions,
            'version': dt.version()
        }
        
    except Exception as e:
        logger.error(f"Error getting basic table info: {e}")
        raise


def get_row_counts_memory_efficient(table_path: str) -> Dict[str, Any]:
    """Get row counts without loading data - memory efficient."""
    try:
        logger.info("🔢 Getting row counts (memory efficient)...")
        
        conn = setup_duckdb_memory_efficient()
        
        # Total rows - no data loading, just metadata scan
        total_rows = conn.execute(f"SELECT COUNT(*) FROM delta_scan('{table_path}')").fetchone()[0]
        logger.info(f"   Total rows: {total_rows:,}")
        
        # Count non-null datetime values
        datetime_count = conn.execute(f'SELECT COUNT("datetime") FROM delta_scan(\'{table_path}\')').fetchone()[0]
        logger.info(f"   Non-null datetime: {datetime_count:,}")
        
        # Count non-null created values
        created_count = conn.execute(f'SELECT COUNT(created) FROM delta_scan(\'{table_path}\')').fetchone()[0]
        logger.info(f"   Non-null created: {created_count:,}")
        
        # Count non-null updated values
        updated_count = conn.execute(f'SELECT COUNT(updated) FROM delta_scan(\'{table_path}\')').fetchone()[0]
        logger.info(f"   Non-null updated: {updated_count:,}")
        
        conn.close()
        
        return {
            'total_rows': total_rows,
            'datetime_count': datetime_count,
            'created_count': created_count,
            'updated_count': updated_count
        }
        
    except Exception as e:
        logger.error(f"Error getting row counts: {e}")
        raise


def sample_datetime_data_memory_efficient(table_path: str, sample_size: int = 100) -> Dict[str, Any]:
    """Sample datetime data using memory-efficient patterns."""
    try:
        logger.info(f"📊 Sampling {sample_size} records (memory efficient)...")
        
        conn = setup_duckdb_memory_efficient()
        
        # Memory-efficient sampling - only get essential columns
        sample_query = f"""
            SELECT 
                scene_id,
                collection,
                platform,
                "datetime",
                created,
                updated,
                year,
                month,
                day
            FROM delta_scan('{table_path}')
            USING SAMPLE {sample_size}
            ORDER BY "datetime" DESC
            LIMIT {sample_size}
        """
        
        sample_data = conn.execute(sample_query).fetchall()
        
        if not sample_data:
            logger.error("No sample data retrieved")
            return {}
        
        logger.info(f"📈 Sample Analysis ({len(sample_data)} records):")
        
        # Analyze datetime ranges
        datetimes = [row[3] for row in sample_data if row[3] is not None]
        if datetimes:
            min_dt = min(datetimes)
            max_dt = max(datetimes)
            logger.info(f"   DateTime range: {min_dt} to {max_dt}")
        
        # Check partition alignment (memory efficient)
        year_mismatches = 0
        month_mismatches = 0
        null_datetime = 0
        
        for row in sample_data:
            if row[3] is None:  # datetime is null
                null_datetime += 1
            else:
                dt = row[3]
                if hasattr(dt, 'year') and hasattr(dt, 'month'):
                    if row[6] != dt.year:  # year partition mismatch
                        year_mismatches += 1
                    if row[7] != dt.month:  # month partition mismatch
                        month_mismatches += 1
        
        logger.info(f"   Null datetime values: {null_datetime}")
        logger.info(f"   Year partition alignment: {len(sample_data) - year_mismatches}/{len(sample_data)} correct")
        logger.info(f"   Month partition alignment: {len(sample_data) - month_mismatches}/{len(sample_data)} correct")
        
        if year_mismatches > 0 or month_mismatches > 0:
            logger.warning(f"⚠️  PARTITION MISALIGNMENT DETECTED!")
            logger.warning(f"   Year mismatches: {year_mismatches}")
            logger.warning(f"   Month mismatches: {month_mismatches}")
        
        # Show sample records (first 3 only to avoid memory issues)
        logger.info(f"📋 Sample records (first 3):")
        for i, row in enumerate(sample_data[:3]):
            logger.info(f"   {i+1}. scene_id: {row[0]}")
            logger.info(f"      collection: {row[1]}, platform: {row[2]}")
            logger.info(f"      datetime: {row[3]}")
            logger.info(f"      created: {row[4]}, updated: {row[5]}")
            logger.info(f"      partitions - year: {row[6]}, month: {row[7]}, day: {row[8]}")
            logger.info("")
        
        conn.close()
        
        return {
            'sample_size': len(sample_data),
            'datetime_range': (min_dt, max_dt) if datetimes else None,
            'year_mismatches': year_mismatches,
            'month_mismatches': month_mismatches,
            'null_datetime': null_datetime
        }
        
    except Exception as e:
        logger.error(f"Error sampling datetime data: {e}")
        raise


def check_datetime_vs_partitions_memory_efficient(table_path: str) -> Dict[str, Any]:
    """Check datetime vs partition alignment across entire table - memory efficient."""
    try:
        logger.info("🔍 Checking datetime vs partition alignment (memory efficient)...")
        
        conn = setup_duckdb_memory_efficient()
        
        # Memory-efficient check - no data loading, just aggregation
        alignment_query = f"""
            SELECT 
                SUM(CASE WHEN year = EXTRACT(year FROM "datetime") THEN 1 ELSE 0 END) as year_aligned,
                SUM(CASE WHEN month = EXTRACT(month FROM "datetime") THEN 1 ELSE 0 END) as month_aligned,
                COUNT(*) as total_checked,
                COUNT("datetime") as datetime_not_null
            FROM delta_scan('{table_path}')
            WHERE "datetime" IS NOT NULL
        """
        
        result = conn.execute(alignment_query).fetchone()
        
        if result:
            year_aligned, month_aligned, total_checked, datetime_not_null = result
            
            logger.info(f"📊 Partition Alignment Analysis:")
            logger.info(f"   Records with datetime: {datetime_not_null:,}")
            logger.info(f"   Year alignment: {year_aligned:,}/{datetime_not_null:,} ({year_aligned/datetime_not_null*100:.1f}%)")
            logger.info(f"   Month alignment: {month_aligned:,}/{datetime_not_null:,} ({month_aligned/datetime_not_null*100:.1f}%)")
            
            if year_aligned < datetime_not_null or month_aligned < datetime_not_null:
                logger.error("🚨 CRITICAL: Partition misalignment detected!")
                logger.error(f"   Year misaligned: {datetime_not_null - year_aligned:,} records")
                logger.error(f"   Month misaligned: {datetime_not_null - month_aligned:,} records")
            else:
                logger.info("✅ All partitions correctly aligned with datetime field")
        
        conn.close()
        
        return {
            'year_aligned': year_aligned,
            'month_aligned': month_aligned,
            'total_checked': total_checked,
            'datetime_not_null': datetime_not_null
        }
        
    except Exception as e:
        logger.error(f"Error checking partition alignment: {e}")
        raise


def main():
    parser = argparse.ArgumentParser(
        description="Memory-efficient datetime semantics investigation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("--quick", action="store_true",
                       help="Quick analysis (sample only, skip full table checks)")
    
    args = parser.parse_args()
    
    logger.info("🔍 Memory-Efficient DateTime Semantics Investigation")
    logger.info("=" * 70)
    logger.info("⚠️  CRITICAL: Understanding datetime fields before restructuring")
    logger.info(f"📍 Table: {TABLE_PATH}")
    logger.info("💾 Using memory-efficient patterns (5.5GB limit, 2 threads)")
    
    start_time = time.time()
    
    try:
        # Step 1: Basic table info (no data loading)
        basic_info = get_basic_table_info(TABLE_PATH)
        
        # Step 2: Row counts (memory efficient)
        count_info = get_row_counts_memory_efficient(TABLE_PATH)
        
        # Step 3: Sample data analysis
        sample_info = sample_datetime_data_memory_efficient(TABLE_PATH, 100)
        
        # Step 4: Full table partition alignment check (unless quick mode)
        if not args.quick:
            alignment_info = check_datetime_vs_partitions_memory_efficient(TABLE_PATH)
        else:
            alignment_info = None
        
        # Summary and critical findings
        duration = time.time() - start_time
        logger.info("")
        logger.info("📋 INVESTIGATION SUMMARY")
        logger.info("=" * 50)
        logger.info(f"⏱️  Analysis duration: {duration:.2f}s")
        
        # Critical findings
        critical_issues = []
        
        if sample_info.get('year_mismatches', 0) > 0 or sample_info.get('month_mismatches', 0) > 0:
            critical_issues.append("Partition misalignment in sample data")
        
        if alignment_info and (alignment_info['year_aligned'] < alignment_info['datetime_not_null'] or 
                              alignment_info['month_aligned'] < alignment_info['datetime_not_null']):
            critical_issues.append("Partition misalignment in full table")
        
        null_datetime_pct = (count_info['total_rows'] - count_info['datetime_count']) / count_info['total_rows'] * 100
        if null_datetime_pct > 1:  # More than 1% null
            critical_issues.append(f"High null datetime percentage: {null_datetime_pct:.1f}%")
        
        # Recommendations
        logger.info("")
        logger.info("🎯 CRITICAL FINDINGS & RECOMMENDATIONS:")
        
        if critical_issues:
            logger.error("🚨 CRITICAL ISSUES FOUND:")
            for issue in critical_issues:
                logger.error(f"   ❌ {issue}")
            logger.error("")
            logger.error("🛑 DO NOT PROCEED with restructuring until these issues are resolved!")
            logger.error("🛑 The 'datetime' field may not represent satellite capture time!")
        else:
            logger.info("✅ No critical datetime issues detected")
            logger.info("✅ Partition alignment appears correct")
            logger.info("✅ 'datetime' field appears to represent satellite capture time")
            logger.info("✅ Safe to proceed with date-level partitioning using 'datetime' field")
        
        logger.info("")
        logger.info("📋 Next steps:")
        if critical_issues:
            logger.info("1. ❌ STOP - Do not proceed with restructuring")
            logger.info("2. 🔍 Investigate datetime field semantics")
            logger.info("3. 🔧 Fix datetime issues before any table changes")
        else:
            logger.info("1. ✅ Datetime field validation passed")
            logger.info("2. ✅ Safe to proceed with table restructuring")
            logger.info("3. 📅 Use 'datetime' field for date-level partitioning")
        
        return 0 if not critical_issues else 1
        
    except Exception as e:
        logger.error(f"❌ Investigation failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

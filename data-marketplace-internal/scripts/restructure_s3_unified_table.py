#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
S3 Unified STAC Table Restructuring Script

Restructures the unified_stac_table to use date-level partitions and 256MB parquet files.
This is a wrapper around the general restructure_delta_table.py script with your specific settings.

Usage:
    # Dry run first (recommended)
    uv run scripts/restructure_s3_unified_table.py --dry-run

    # Full restructure
    uv run scripts/restructure_s3_unified_table.py

    # Custom target location
    uv run scripts/restructure_s3_unified_table.py --target-suffix "_v2"

    # Resume interrupted operation
    uv run scripts/restructure_s3_unified_table.py --resume
"""

import argparse
import logging
import subprocess
import sys
import time
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Configuration for your specific table
SOURCE_TABLE_PATH = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table"
TARGET_TABLE_BASE = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_restructured"
DATE_COLUMN = "datetime"  # Adjust if your date column has a different name
TARGET_FILE_SIZE = 268435456  # 256MB in bytes
CHUNK_SIZE = 5000  # Rows per chunk (proven optimal from your existing scripts)


def main():
    parser = argparse.ArgumentParser(
        description="Restructure S3 unified STAC table with date partitions and 256MB files",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("--target-suffix", type=str, default="",
                       help="Suffix to add to target table name (default: '_restructured')")
    parser.add_argument("--date-column", type=str, default=DATE_COLUMN,
                       help=f"Date column for partitioning (default: {DATE_COLUMN})")
    parser.add_argument("--target-file-size", type=int, default=TARGET_FILE_SIZE,
                       help=f"Target file size in bytes (default: {TARGET_FILE_SIZE // (1024*1024)}MB)")
    parser.add_argument("--chunk-size", type=int, default=CHUNK_SIZE,
                       help=f"Rows per processing chunk (default: {CHUNK_SIZE})")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be done without making changes")
    parser.add_argument("--resume", action="store_true",
                       help="Resume interrupted operation")
    parser.add_argument("--force", action="store_true",
                       help="Overwrite target table if it exists")
    
    args = parser.parse_args()
    
    # Determine target path
    if args.target_suffix:
        target_path = f"{TARGET_TABLE_BASE}{args.target_suffix}"
    else:
        target_path = TARGET_TABLE_BASE
    
    logger.info("🚀 S3 Unified STAC Table Restructuring")
    logger.info("=" * 60)
    logger.info(f"📍 Source: {SOURCE_TABLE_PATH}")
    logger.info(f"📍 Target: {target_path}")
    logger.info(f"📅 Date column: {args.date_column}")
    logger.info(f"📁 Target file size: {args.target_file_size // (1024*1024)}MB")
    logger.info(f"📦 Chunk size: {args.chunk_size:,} rows")
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No changes will be made")
    
    try:
        # Get the directory of this script
        script_dir = Path(__file__).parent
        restructure_script = script_dir / "restructure_delta_table.py"
        
        if not restructure_script.exists():
            logger.error(f"❌ Restructure script not found: {restructure_script}")
            return 1
        
        # Build command arguments
        cmd = ["uv", "run", str(restructure_script), SOURCE_TABLE_PATH, target_path]
        
        # Add optional arguments
        cmd.extend(["--date-column", args.date_column])
        cmd.extend(["--target-file-size", str(args.target_file_size)])
        cmd.extend(["--chunk-size", str(args.chunk_size)])
        
        if args.dry_run:
            cmd.append("--dry-run")
        
        if args.resume:
            cmd.append("--resume")
        
        if args.force:
            cmd.append("--force")
        
        logger.info(f"🔧 Running command: {' '.join(cmd)}")
        logger.info("")
        
        # Execute the restructuring
        start_time = time.time()
        result = subprocess.run(cmd, cwd=script_dir.parent)
        duration = time.time() - start_time
        
        if result.returncode == 0:
            logger.info("")
            logger.info(f"✅ {'[DRY RUN] ' if args.dry_run else ''}Restructuring completed successfully in {duration:.2f}s")
            
            if not args.dry_run:
                logger.info("")
                logger.info("🎉 S3 Unified STAC Table Restructuring Complete!")
                logger.info("📋 Summary of changes:")
                logger.info("   ✅ Converted to date-level partitions")
                logger.info("   ✅ Optimized files to 256MB minimum size")
                logger.info("   ✅ Preserved all original data")
                logger.info("")
                logger.info("🔄 Next steps:")
                logger.info("   1. Validate the restructured table")
                logger.info("   2. Update applications to use new table location")
                logger.info("   3. Consider cleaning up original table after validation")
                logger.info("")
                logger.info(f"📍 New table location: {target_path}")
        else:
            logger.error(f"❌ Restructuring failed with exit code {result.returncode}")
            return result.returncode
            
    except Exception as e:
        logger.error(f"❌ Error during restructuring: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())

#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake Table Validation Script

Validates that a restructured Delta table contains the same data as the original.
Compares row counts, checksums, and data integrity between source and target tables.

Usage:
    # Basic validation
    uv run scripts/validate_restructured_table.py \
        s3://bucket/source-table \
        s3://bucket/restructured-table

    # Detailed validation with sampling
    uv run scripts/validate_restructured_table.py \
        s3://bucket/source-table \
        s3://bucket/restructured-table \
        --detailed --sample-size 10000

    # Quick validation (row counts only)
    uv run scripts/validate_restructured_table.py \
        s3://bucket/source-table \
        s3://bucket/restructured-table \
        --quick
"""

import argparse
import logging
import sys
import time
from typing import Dict, Any, Optional

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_storage_options(table_path: str) -> dict:
    """Setup storage options for S3 or local filesystem."""
    storage_options = {}
    
    if table_path.startswith("s3://"):
        try:
            import subprocess
            bucket = table_path.split("/")[2]
            result = subprocess.run(
                ["aws", "s3api", "get-bucket-location", "--bucket", bucket],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                import json
                location = json.loads(result.stdout).get("LocationConstraint")
                region = location if location else "us-east-1"
                storage_options["AWS_REGION"] = region
            else:
                storage_options["AWS_REGION"] = "us-west-2"
        except Exception:
            storage_options["AWS_REGION"] = "us-west-2"
        
        logger.info("Using IAM role or AWS CLI profile for S3 access")
    
    return storage_options


def get_table_stats(table_path: str, storage_options: dict, table_name: str) -> Dict[str, Any]:
    """Get comprehensive table statistics."""
    try:
        import duckdb
        from deltalake import DeltaTable
        
        logger.info(f"📊 Analyzing {table_name}: {table_path}")
        
        # Get Delta table metadata
        dt = DeltaTable(table_path, storage_options=storage_options)
        
        # Setup DuckDB
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        
        # Configure S3 access if needed
        if table_path.startswith("s3://") and storage_options:
            if "AWS_ACCESS_KEY_ID" in storage_options:
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                        SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                        REGION '{storage_options.get("AWS_REGION", "us-west-2")}'
                    )
                """)
            else:
                conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Get row count
        total_rows_result = conn.execute(f"SELECT COUNT(*) FROM delta_scan('{table_path}')").fetchone()
        total_rows = int(total_rows_result[0]) if total_rows_result and total_rows_result[0] is not None else 0
        
        # Get schema
        schema_result = conn.execute(f"DESCRIBE delta_scan('{table_path}')").fetchall()
        schema = {row[0]: row[1] for row in schema_result}
        
        # Get partition info
        partitions = dt.metadata().partition_columns if hasattr(dt.metadata(), 'partition_columns') else []
        
        # Get file count
        try:
            files = dt.file_uris()
            file_count = len(files)
        except AttributeError:
            files = dt.files()
            file_count = len(files)
        
        # Get unique values in key columns for validation
        unique_scene_ids = None
        unique_cog_keys = None
        
        try:
            if 'scene_id' in schema:
                scene_result = conn.execute(f"SELECT COUNT(DISTINCT scene_id) FROM delta_scan('{table_path}')").fetchone()
                unique_scene_ids = int(scene_result[0]) if scene_result and scene_result[0] is not None else 0
            
            if 'cog_key' in schema:
                cog_result = conn.execute(f"SELECT COUNT(DISTINCT cog_key) FROM delta_scan('{table_path}')").fetchone()
                unique_cog_keys = int(cog_result[0]) if cog_result and cog_result[0] is not None else 0
        except Exception as e:
            logger.warning(f"Could not get unique counts: {e}")
        
        conn.close()
        
        stats = {
            'total_rows': total_rows,
            'schema': schema,
            'partitions': partitions,
            'file_count': file_count,
            'unique_scene_ids': unique_scene_ids,
            'unique_cog_keys': unique_cog_keys,
            'version': dt.version()
        }
        
        logger.info(f"📈 {table_name} stats:")
        logger.info(f"   Rows: {total_rows:,}")
        logger.info(f"   Files: {file_count}")
        logger.info(f"   Partitions: {partitions}")
        logger.info(f"   Schema columns: {len(schema)}")
        if unique_scene_ids is not None:
            logger.info(f"   Unique scene_ids: {unique_scene_ids:,}")
        if unique_cog_keys is not None:
            logger.info(f"   Unique cog_keys: {unique_cog_keys:,}")
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting stats for {table_name}: {e}")
        raise


def validate_data_integrity(source_path: str, target_path: str, 
                           source_storage: dict, target_storage: dict,
                           sample_size: Optional[int] = None) -> bool:
    """Validate data integrity between source and target tables."""
    try:
        import duckdb
        
        logger.info("🔍 Validating data integrity...")
        
        # Setup DuckDB
        conn = duckdb.connect()
        conn.execute("INSTALL delta")
        conn.execute("LOAD delta")
        conn.execute("SET memory_limit='5.5GB'")
        conn.execute("SET threads=2")
        
        # Configure S3 access
        if source_path.startswith("s3://") and source_storage:
            if "AWS_ACCESS_KEY_ID" in source_storage:
                conn.execute(f"""
                    CREATE SECRET s3_secret (
                        TYPE S3,
                        KEY_ID '{source_storage["AWS_ACCESS_KEY_ID"]}',
                        SECRET '{source_storage["AWS_SECRET_ACCESS_KEY"]}',
                        REGION '{source_storage.get("AWS_REGION", "us-west-2")}'
                    )
                """)
            else:
                conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
        
        # Sample data for comparison if requested
        sample_clause = f"USING SAMPLE {sample_size}" if sample_size else ""
        
        # Compare checksums of key columns
        logger.info("🔢 Comparing data checksums...")
        
        # Get checksum from source
        source_checksum_query = f"""
            SELECT 
                COUNT(*) as row_count,
                COUNT(DISTINCT scene_id) as unique_scenes,
                COUNT(DISTINCT cog_key) as unique_cogs,
                bit_xor(hash(scene_id || '|' || cog_key)) as data_hash
            FROM delta_scan('{source_path}') {sample_clause}
        """
        
        source_result = conn.execute(source_checksum_query).fetchone()
        
        # Get checksum from target
        target_checksum_query = f"""
            SELECT 
                COUNT(*) as row_count,
                COUNT(DISTINCT scene_id) as unique_scenes,
                COUNT(DISTINCT cog_key) as unique_cogs,
                bit_xor(hash(scene_id || '|' || cog_key)) as data_hash
            FROM delta_scan('{target_path}') {sample_clause}
        """
        
        target_result = conn.execute(target_checksum_query).fetchone()
        
        conn.close()
        
        # Compare results
        if source_result and target_result:
            source_rows, source_scenes, source_cogs, source_hash = source_result
            target_rows, target_scenes, target_cogs, target_hash = target_result
            
            logger.info(f"📊 Checksum comparison:")
            logger.info(f"   Source: {source_rows:,} rows, {source_scenes:,} scenes, {source_cogs:,} cogs")
            logger.info(f"   Target: {target_rows:,} rows, {target_scenes:,} scenes, {target_cogs:,} cogs")
            
            # Check if data matches
            data_matches = (
                source_rows == target_rows and
                source_scenes == target_scenes and
                source_cogs == target_cogs and
                source_hash == target_hash
            )
            
            if data_matches:
                logger.info("✅ Data integrity validation PASSED")
                return True
            else:
                logger.error("❌ Data integrity validation FAILED")
                logger.error("   Data checksums do not match between source and target")
                return False
        else:
            logger.error("❌ Could not compute checksums for comparison")
            return False
        
    except Exception as e:
        logger.error(f"❌ Error validating data integrity: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="Validate restructured Delta Lake table",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("source_path", help="Source Delta Lake table path")
    parser.add_argument("target_path", help="Target Delta Lake table path")
    parser.add_argument("--quick", action="store_true",
                       help="Quick validation (row counts only)")
    parser.add_argument("--detailed", action="store_true",
                       help="Detailed validation with data integrity checks")
    parser.add_argument("--sample-size", type=int,
                       help="Sample size for detailed validation (default: full table)")
    
    args = parser.parse_args()
    
    logger.info("🔍 Delta Lake Table Validation")
    logger.info("=" * 50)
    logger.info(f"📍 Source: {args.source_path}")
    logger.info(f"📍 Target: {args.target_path}")
    
    start_time = time.time()
    
    try:
        # Setup storage options
        source_storage = setup_storage_options(args.source_path)
        target_storage = setup_storage_options(args.target_path)
        
        # Get table statistics
        source_stats = get_table_stats(args.source_path, source_storage, "Source")
        target_stats = get_table_stats(args.target_path, target_storage, "Target")
        
        # Basic validation
        logger.info("")
        logger.info("🔍 Basic Validation:")
        
        validation_passed = True
        
        # Check row counts
        if source_stats['total_rows'] == target_stats['total_rows']:
            logger.info(f"✅ Row counts match: {source_stats['total_rows']:,}")
        else:
            logger.error(f"❌ Row counts differ: source={source_stats['total_rows']:,}, target={target_stats['total_rows']:,}")
            validation_passed = False
        
        # Check schema columns
        source_cols = set(source_stats['schema'].keys())
        target_cols = set(target_stats['schema'].keys())
        
        if source_cols == target_cols:
            logger.info(f"✅ Schema columns match: {len(source_cols)} columns")
        else:
            logger.error(f"❌ Schema columns differ")
            logger.error(f"   Source only: {source_cols - target_cols}")
            logger.error(f"   Target only: {target_cols - source_cols}")
            validation_passed = False
        
        # Check unique counts
        if (source_stats['unique_scene_ids'] is not None and 
            target_stats['unique_scene_ids'] is not None):
            if source_stats['unique_scene_ids'] == target_stats['unique_scene_ids']:
                logger.info(f"✅ Unique scene_ids match: {source_stats['unique_scene_ids']:,}")
            else:
                logger.error(f"❌ Unique scene_ids differ: source={source_stats['unique_scene_ids']:,}, target={target_stats['unique_scene_ids']:,}")
                validation_passed = False
        
        # Detailed validation if requested
        if args.detailed and not args.quick:
            logger.info("")
            integrity_passed = validate_data_integrity(
                args.source_path, args.target_path,
                source_storage, target_storage,
                args.sample_size
            )
            validation_passed = validation_passed and integrity_passed
        
        # Summary
        duration = time.time() - start_time
        logger.info("")
        logger.info(f"📋 Validation Summary:")
        logger.info(f"   Duration: {duration:.2f}s")
        logger.info(f"   Source partitions: {source_stats['partitions']}")
        logger.info(f"   Target partitions: {target_stats['partitions']}")
        logger.info(f"   Source files: {source_stats['file_count']}")
        logger.info(f"   Target files: {target_stats['file_count']}")
        
        if validation_passed:
            logger.info("🎉 Overall validation: PASSED")
            logger.info("✅ Restructured table is valid and contains the same data as source")
        else:
            logger.error("❌ Overall validation: FAILED")
            logger.error("⚠️  Restructured table has data discrepancies")
        
        return 0 if validation_passed else 1
        
    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())

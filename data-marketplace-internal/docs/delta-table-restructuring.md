# Delta Lake Table Restructuring Guide

This guide explains how to restructure Delta Lake tables to use date-level partitions and 256MB parquet files using the provided scripts.

## Overview

The restructuring process converts existing Delta tables from year/month partitioning to date-level partitioning while optimizing file sizes to 256MB minimum. This improves query performance for date-based filtering and reduces the number of small files.

## Architecture

### Hybrid Approach: DuckDB + Delta Lake

We use a hybrid approach that combines the strengths of both technologies:

- **DuckDB**: Efficient analytical processing with low memory usage
- **Delta Lake Python**: Full read/write support for Delta format with ACID guarantees

### Key Benefits

1. **Memory Efficient**: Chunked processing handles large tables without OOMs
2. **Safe**: Copies to new location, preserving original table
3. **Resumable**: Can restart interrupted operations
4. **Validated**: Comprehensive data integrity checks
5. **Optimized**: Achieves target 256MB file sizes

## Scripts Overview

### 1. `restructure_delta_table.py`
General-purpose script for restructuring any Delta table with date partitions.

**Features:**
- Auto-detects date columns
- Chunked processing for memory efficiency
- Configurable target file sizes
- Dry-run mode for safety
- Progress tracking

### 2. `restructure_s3_unified_table.py`
Wrapper script specifically for your unified_stac_table with pre-configured settings.

**Pre-configured:**
- Source: `s3://tf-datalake-bucket/deltalake-tables/unified_stac_table`
- Target: `s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_restructured`
- Date column: `datetime`
- Target file size: 256MB
- Chunk size: 5,000 rows

### 3. `validate_restructured_table.py`
Validates that restructured table contains identical data to the original.

**Validation checks:**
- Row count comparison
- Schema validation
- Unique value counts
- Data integrity checksums

## Usage Examples

### Quick Start (Recommended)

```bash
# 1. Dry run first to see what would happen
uv run scripts/restructure_s3_unified_table.py --dry-run

# 2. Run the actual restructuring
uv run scripts/restructure_s3_unified_table.py

# 3. Validate the results
uv run scripts/validate_restructured_table.py \
    s3://tf-datalake-bucket/deltalake-tables/unified_stac_table \
    s3://tf-datalake-bucket/deltalake-tables/unified_stac_table_restructured \
    --detailed
```

### Advanced Usage

```bash
# Custom target location
uv run scripts/restructure_s3_unified_table.py --target-suffix "_v2"

# Different date column
uv run scripts/restructure_s3_unified_table.py --date-column "timestamp"

# Smaller chunks for memory-constrained environments
uv run scripts/restructure_s3_unified_table.py --chunk-size 2000

# Resume interrupted operation
uv run scripts/restructure_s3_unified_table.py --resume
```

### General Script Usage

```bash
# Restructure any Delta table
uv run scripts/restructure_delta_table.py \
    s3://bucket/source-table \
    s3://bucket/target-table \
    --date-column datetime \
    --target-file-size 268435456 \
    --dry-run

# Validate any restructured table
uv run scripts/validate_restructured_table.py \
    s3://bucket/source-table \
    s3://bucket/target-table \
    --detailed --sample-size 10000
```

## Technical Details

### Memory Management

The scripts are designed for your 7GB RAM environment:

- **DuckDB memory limit**: 5.5GB (leaves 1.5GB for OS/other processes)
- **Thread count**: 2 (reduces memory pressure)
- **Chunk size**: 5,000 rows (proven optimal from your existing scripts)
- **Low concurrency**: `max_concurrent_tasks=2` for Delta operations

### Partitioning Strategy

**Before (year/month partitioning):**
```
table/
├── year=2023/month=01/
├── year=2023/month=02/
└── year=2023/month=03/
```

**After (date partitioning):**
```
table/
├── date=2023-01-01/
├── date=2023-01-02/
└── date=2023-01-03/
```

### File Size Optimization

- **Target**: 256MB minimum parquet files
- **Method**: Delta Lake `optimize.compact()` with target size
- **Benefit**: Reduces small file overhead, improves query performance

## Process Flow

1. **Analysis**: Examine source table schema and partitions
2. **Date Extraction**: Identify unique dates for processing
3. **Chunked Processing**: Process each date in memory-efficient chunks
4. **Delta Writing**: Write to new table with date partitioning
5. **Optimization**: Compact files to achieve 256MB target size
6. **Validation**: Verify data integrity between source and target

## Error Handling

### Resume Capability
If the process is interrupted, you can resume using:
```bash
uv run scripts/restructure_s3_unified_table.py --resume
```

### Validation Failures
If validation fails, check:
1. Network connectivity to S3
2. AWS credentials and permissions
3. Source table accessibility
4. Available disk space (for local operations)

### Memory Issues
If you encounter OOM errors:
1. Reduce chunk size: `--chunk-size 2000`
2. Ensure no other memory-intensive processes are running
3. Check available system memory

## Best Practices

### Before Running
1. **Always run dry-run first**: `--dry-run`
2. **Check source table health**: Run your existing cleanup scripts
3. **Verify AWS credentials**: Ensure S3 access is working
4. **Monitor system resources**: Check available memory and disk space

### During Execution
1. **Monitor progress**: Scripts provide detailed logging
2. **Check system resources**: Watch memory and CPU usage
3. **Be patient**: Large tables take time to process

### After Completion
1. **Validate results**: Use the validation script
2. **Compare performance**: Test query performance on new table
3. **Update applications**: Point to new table location gradually
4. **Clean up**: Remove original table only after thorough validation

## Troubleshooting

### Common Issues

**"No date column found"**
- Specify date column explicitly: `--date-column your_date_column`
- Check available columns in source table

**"Memory limit exceeded"**
- Reduce chunk size: `--chunk-size 2000`
- Ensure DuckDB memory limit is appropriate for your system

**"S3 access denied"**
- Verify AWS credentials: `aws s3 ls s3://your-bucket/`
- Check IAM permissions for source and target buckets

**"Table already exists"**
- Use `--force` to overwrite existing target table
- Or choose a different target location

### Performance Optimization

For very large tables (>1TB):
1. Consider processing in date ranges
2. Use larger chunk sizes if memory allows
3. Run during off-peak hours
4. Monitor S3 request rates

## Integration with Existing Scripts

The restructuring scripts are designed to work alongside your existing maintenance scripts:

1. **Before restructuring**: Run `clean_delta_table.py` to optimize source
2. **After restructuring**: Use `clean_delta_table.py` on target for ongoing maintenance
3. **Regular validation**: Periodically run validation scripts

## Next Steps

After successful restructuring:

1. **Performance Testing**: Compare query performance
2. **Application Updates**: Gradually migrate applications
3. **Monitoring**: Set up monitoring for the new table
4. **Documentation**: Update your data catalog and documentation
5. **Cleanup**: Archive or remove the original table after validation period

## Support

For issues or questions:
1. Check the script logs for detailed error messages
2. Verify your environment meets the requirements
3. Test with smaller tables first
4. Review this documentation for troubleshooting steps

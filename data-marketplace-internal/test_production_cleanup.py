# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Test script to demonstrate Delta Lake cleanup operations on production table.
"""

import asyncio
import logging
from deltalake import DeltaTable
from src.data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_production_cleanup():
    """Test cleanup operations on the production unified_stac_table."""
    
    table_path = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table"
    storage_options = {"AWS_REGION": "us-west-2"}
    
    logger.info("🔍 Testing cleanup operations on production table")
    logger.info(f"📍 Table: {table_path}")
    
    # Load Delta table directly
    dt = DeltaTable(table_path, storage_options=storage_options)
    
    logger.info("📊 Before cleanup:")
    logger.info(f"  - Delta version: {dt.version()}")
    logger.info(f"  - Active files: {len(dt.file_uris())}")
    
    # Test vacuum dry run
    logger.info("🧹 Testing vacuum (dry run with 1 hour retention)...")
    try:
        dry_run_files = dt.vacuum(retention_hours=1, dry_run=True, enforce_retention_duration=False)
        logger.info(f"📋 Found {len(dry_run_files)} files that could be cleaned up")
        
        if dry_run_files:
            logger.info("📋 Example files to clean:")
            for i, file in enumerate(dry_run_files[:3]):
                logger.info(f"  {i+1}. {file}")
    except Exception as e:
        logger.warning(f"⚠️  Vacuum dry run failed: {e}")
    
    # Test optimize
    logger.info("🔧 Testing optimize...")
    try:
        optimize_result = dt.optimize.compact()
        files_added = optimize_result.get('files_added', 0)
        files_removed = optimize_result.get('files_removed', 0)
        logger.info(f"✅ Optimize completed: {files_removed} small files → {files_added} optimized files")
    except Exception as e:
        logger.warning(f"⚠️  Optimize failed: {e}")
    
    logger.info("📊 After cleanup:")
    logger.info(f"  - Delta version: {dt.version()}")
    logger.info(f"  - Active files: {len(dt.file_uris())}")
    
    # Test our ingester cleanup method
    logger.info("🧪 Testing ingester cleanup method...")
    try:
        ingester = DeltaStacIngester(table_path, storage_options=storage_options)
        await ingester._perform_table_cleanup(
            skip_vacuum=False,
            skip_optimize=False, 
            vacuum_retention_hours=1
        )
        logger.info("✅ Ingester cleanup method completed")
    except Exception as e:
        logger.warning(f"⚠️  Ingester cleanup failed: {e}")

if __name__ == "__main__":
    asyncio.run(test_production_cleanup())
